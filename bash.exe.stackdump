Stack trace:
Frame         Function      Args
0007FFFFABF0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AF0) msys-2.0.dll+0x1FE8E
0007FFFFABF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEC8) msys-2.0.dll+0x67F9
0007FFFFABF0  000210046832 (000210286019, 0007FFFFAAA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABF0  000210068E24 (0007FFFFAC00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAED0  00021006A225 (0007FFFFAC00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC29560000 ntdll.dll
7FFC273E0000 KERNEL32.DLL
7FFC26D80000 KERNELBASE.dll
7FFC224D0000 apphelp.dll
7FFC27C30000 USER32.dll
7FFC26870000 win32u.dll
7FFC276B0000 GDI32.dll
7FFC26AC0000 gdi32full.dll
7FFC26A10000 msvcp_win.dll
7FFC27210000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC276E0000 advapi32.dll
7FFC28AA0000 msvcrt.dll
7FFC28E30000 sechost.dll
7FFC293F0000 RPCRT4.dll
7FFC25C40000 CRYPTBASE.DLL
7FFC26740000 bcryptPrimitives.dll
7FFC293A0000 IMM32.DLL
