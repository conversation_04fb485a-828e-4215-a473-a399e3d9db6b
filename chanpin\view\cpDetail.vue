<template>
	<view>
		<u-navbar back-icon-color="#FFFFFF" title="成品库存资料" title-color="#FFFFFF" :title-bold="true"
			:background="background">
		</u-navbar>
		<view class="top-box">
			<view class="myCard">
				<view class="cardTopName">编号：{{this.item.FabricGoodsNo}} 名称：{{this.item.FabricGoodsName}}</view>
				<view class="cardRow1" v-if="this.item.GoodsWeight">
					<view>规格：{{this.item.GoodsWidth}}*{{this.item.GoodsWeight}}</view>
				</view>
				<view class="cardRow1">
					<view>仓库：{{this.item.StoreName}} 所属：{{this.item.OwnerName}}</view>
				</view>
				<view class="cardRow1">
					<view>等级：{{this.item.GoodsGradeName}}</view>
				</view>
				<view class="cardRow1">
					<view>备注：{{this.item.GoodsRemark}}</view>
				</view>
			</view>
		</View>

		<scroll-view scroll-y="true" :style="{height: scrollHeight}" 
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore">
			<view v-if="list.length > 0">
				<view v-for="(item, index) in list" :key="index" @click="StoreGoodsCodeCardClickFun(item)">
					<view class="myCard">
						<view class="cardRow1">
							<view>颜色：{{item.GoodsCodeNo}} {{item.GoodsCodeName}}</view>
						</view>
						<view class="cardRow1">
							<view class="mr26">匹数：{{item.Roll}}匹</view>
							<view class="mr26">数量：{{item.Qty}}{{item.UnitName}}</view>
						</view>
						<view class="cardRow1">
							<view class="mr26">预约：{{item.AllocatedRoll}}匹</view>
							<view class="mr26">可用：{{item.UseRoll}}匹</view>
						</view>
						<view class="cardRow1" v-if="item.Price > 0">
							<view class="mr26">单价：{{item.Price}}/{{item.UnitName}}</view>
						</view>
					</view>
				</view>
				<getMore :isMore="isMore" nullMsg="已加载全部~"></getMore>
				<view class="h200"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	let that = '';
	//import { crmChanpinApi } from '@/static/utils/api.js'
	import dataNull from '@/components/dataNull/dataNull.vue';
	import util, {
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util.js';
	export default {
		components: {
			dataNull
		},
		data() {
			return {
				triggered: false,
				scrollHeight: '667px',
				customerID: 0,
				GoodsCodeNo: '',
				GoodsCodeName: '',
				tabBars: [],
				list: [],
				detail: {},
				background: {
					'background-image': 'linear-gradient(45deg, #007aff, rgb(12, 168, 234))'
				},
				customStyle: {
					backgroundColor: 'rgb(13, 159, 224)',
					color: '#FFFFFF',
					border: '0',
					fontSize: '32',
					outline: 'none'
				},
				lbt: [],
				pageType: ''
			}
		},
		onLoad(e) {
			that = this;

			that.detail = uni.$cpDetail;
			console.log("------" + that.detail.FabricGoodsName);
			this.customerID = e.customerid;
			this.item = JSON.parse(decodeURIComponent(e.obj));
			this.getStoreGoodsCodeData(e);
		},

		methods: {
			getStoreGoodsCodeData: function(e) {
				let iii = that.leftA;
				console.log("this.item.StoreNameID===" + this.item.StoreNameID);
				console.log("this.item.OwnerID===" + this.item.OwnerID);
				console.log("this.item.CustomerID===" + this.customerID);
				console.log("this.item.FabricGoodsID===" + this.item.FabricGoodsID);
				console.log("this.item.GoodsGradeName===" + this.item.GoodsGradeName);
				console.log("this.item.GoodsRemark===" + this.item.GoodsRemark);
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.StoreGoods.GetStoreGoodsMasterGoodsCode',
							params: [{
								name: 'SID',
								value: this.item.StoreNameID
							}, {
								name: 'OID',
								value: this.item.OwnerID
							}, {
								name: 'CID',
								value: this.customerID
							}, {
								name: 'FGID',
								value: this.item.FabricGoodsID
							}, {
								name: 'GradeName',
								value: this.item.GoodsGradeName
							}, {
								name: 'GoodsRemark',
								value: this.item.GoodsRemark
							}, {
								name: 'GCNo',
								value: '%' + this.GoodsCodeNo + '%'
							}, {
								name: 'GCName',
								value: '%' + this.GoodsCodeName + '%'
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						if (that.pageIndex == 1) {
							that.list = [];
						}
						if (res.data == 20) {
							that.pageIndex += 1;
							that.isMore = true;
						} else {
							that.isMore = false;
						}
						that.list = that.list.concat(data);
						console.log("----->>" + JSON.stringify(that.list));
					},
				})
			},

			// 客户点击方法
			StoreGoodsCodeCardClickFun: function(item) {
				uni.$emit('bjdKehuBindFun', {
					CustomerID: item.CustomerID,
					CustomerName: item.CustomerName,
					PlanDepartmentID: item.PlanDepartmentID,
					PlanDepartmentName: item.PlanDepartmentName,
					SaleUserID: item.SaleUserID,
					SaleUserName: item.SaleUserName,
					Remark: item.Remark,
					CustomerAddress: item.CustomerAddress,
					CustomerPhone: item.CustomerPhone,
					CustomerLinkName: item.CustomerLinkName,
					/*
					clientName: item.clientName,
					clientId: item._id
					*/
				})
				uni.navigateBack()
			},
		}
	}
</script>

<style lang='scss'>
	page {
		background-color: #F8F8F8;
		padding-bottom: 100rpx;
	}

	.swiperClass {
		margin-top: 16rpx;
	}

	.xiangqing {
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
		margin: 16rpx 0;
	}

	.spXQBox {
		width: 698rpx;
		margin: 0 26rpx;
		background-color: #FFFFFF;
		padding: 26rpx;
		box-sizing: border-box;
		border-radius: 16rpx;
		box-shadow: #d8d8d8 0px 0px 16rpx;
	}

	.table {
		width: 100%;
		border: 1rpx solid #E7E7E7;
		margin: 32rpx 0;
	}

	.row {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1rpx solid #E7E7E7;
	}

	.row>view:first-child {
		width: 180rpx;
		height: 100%;
		font-size: 28rpx;
		padding: 0 16rpx;
		box-sizing: border-box;
	}

	.row>view:last-child {
		width: 506rpx;
		height: 100%;
		font-size: 28rpx;
		padding: 16rpx;
		box-sizing: border-box;
		border-left: 1rpx solid #E7E7E7;
	}

	.imgXQ {
		width: 100%;
		background-color: #FFFFFF;
		margin-top: 32rpx;
	}

	.imgXqtp {
		width: 100%;
	}

	.flexRow {
		display: flex;
	}

	.xqTitle {
		font-size: 16px;
		font-weight: bold;
		margin: 26rpx;
	}

	.addBtn {
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		background-image: linear-gradient(45deg, #007aff, #00aaff);
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 80rpx;
		right: 26rpx;
		z-index: 100;
	}

	.addBtn:active {
		background-image: linear-gradient(45deg, #00aaff, #007aff);
	}

	.shopBtn {
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		background-image: linear-gradient(45deg, #007aff, #00aaff);
		display: flex;
		align-items: center;
		font-size: 26rpx;
		padding: 0;
		color: #FFFFFF;
		font-weight: bold;
		justify-content: center;
		position: fixed;
		bottom: 190rpx;
		right: 26rpx;
		z-index: 100;
	}

	.shopBtn:active {
		background-image: linear-gradient(45deg, #00aaff, #007aff);
	}

	.nameCardBtn {
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		background-image: linear-gradient(45deg, #007aff, #00aaff);
		display: flex;
		align-items: center;
		font-size: 26rpx;
		padding: 0;
		color: #FFFFFF;
		font-weight: bold;
		justify-content: center;
		position: fixed;
		bottom: 300rpx;
		right: 26rpx;
		z-index: 100;
	}

	.nameCardBtn:active {
		background-image: linear-gradient(45deg, #00aaff, #007aff);
	}
</style>
