const toNumber = 1 // 转化为数字格式
const hundred = 100
const tenThousand = 10000
// 字典，用于判断进退位
export const baseDict = {
  shortage_roll: hundred, // 欠货匹数
  shortage_weight: tenThousand, // 欠货数量
  shortage_length: tenThousand, // 欠货长度
  book_roll: hundred, // 预约匹数
  before_check_roll: hundred, // 盘前匹数
  after_check_roll: hundred, // 实盘匹数
  diff_roll: hundred, // 差异匹数
  check_roll: hundred, // 实盘匹数
  push_roll: hundred, // 实盘匹数
  diff_weight: tenThousand, // 差异数量
  push_weight: tenThousand, // 差异数量
  before_check_weight: tenThousand, // 盘前数量
  after_check_weight: tenThousand, // 实盘数量
  check_weight: tenThousand, // 实盘数量
  standard_price: tenThousand, // 标准单价
  standard_weight_error: tenThousand, // 标准空差
  settle_weight_error: tenThousand, // 结算空差
  convert_total_score: hundred, // 折算评分
  upper_limit: hundred, // 上限
  lower_limit: hundred, // 下限
  use_yarn_quantity: tenThousand,
  send_yarn_quantity: tenThousand,
  bulk_weight: tenThousand,
  total_weight: tenThousand,
  unit_price: tenThousand,
  other_price: hundred,
  other_weight: tenThousand,
  total_price: hundred,
  paid_price: hundred,
  unpaid_price: hundred,
  total_use_yarn_quantity: tenThousand,
  record_use_yarn_quantity: tenThousand,
  src_use_yarn_quantity: tenThousand,
  yarn_loss: hundred,
  yarn_ratio: hundred,
  total_whole_count: toNumber,
  total_bulk_count: toNumber,
  record_whole_count: toNumber,
  record_bulk_count: toNumber,
  whole_count: toNumber,
  bulk_count: toNumber,
  src_bulk_count: toNumber,
  src_whole_count: toNumber,
  use_yarn_bulk_count: toNumber,
  use_yarn_whole_count: toNumber,
  whole_weight: tenThousand,
  record_weight: tenThousand,
  act_roll: hundred,
  act_weight: tenThousand,
  weight: tenThousand,
  pre_roll: hundred,
  pre_weight: tenThousand,
  roll: hundred,
  grey_fabric_weight: tenThousand,
  increase_weight: tenThousand,
  paper_tube_weight: tenThousand,
  dye_weight: tenThousand,
  dye_roll: hundred,
  out_roll: hundred,
  out_weight: tenThousand,
  stock_weight: tenThousand,
  stock_roll: hundred,
  piece_count: hundred,
  wait_dye_roll: hundred,
  wait_dye_weight: tenThousand,
  whole_piece_count: hundred,
  bulk_piece_count: hundred,
  dnf_loss: hundred,
  planed_roll: hundred,
  planed_weight: tenThousand,
  scheduling_roll: hundred,
  scheduling_weight: tenThousand,
  produced_roll: hundred,
  produced_weight: tenThousand,
  use_stock_roll: hundred,
  can_scheduling_roll: hundred,
  this_scheduling_roll: hundred,
  rtn_piece_count: hundred,
  rtn_weight: tenThousand,
  not_rtn_piece_count: hundred,
  not_rtn_weight: tenThousand,
  arranged_roll: hundred,
  arranged_weight: tenThousand,
  payment_term: toNumber,
  weight_of_fabric: tenThousand,
  process_price: tenThousand,
  invoice_header: toNumber,
  machinescls_num: toNumber,
  weaving_loss: hundred,
  warp_density: hundred,
  weft_density: hundred,
  reed_inner_width: hundred,
  reed_outer_width: hundred,
  reed_no: hundred,
  penetration_number: hundred,
  upper_weft_density: hundred,
  lower_weft_density: hundred,
  gf_theory_gram_width: hundred,
  total_warp_pieces: hundred,
  total_use_weight: tenThousand,
  net_use_weight: tenThousand,
  use_weight: tenThousand,
  machines_num: toNumber,
  material_loss: hundred,
  material_ratio: hundred,
  inspect_total_weight: tenThousand,
  inspect_total_roll: hundred,
  total_roll: hundred,
  useful_width: hundred,
  edge_width: hundred,
  inspect_ratio: hundred,
  actually_weight: tenThousand,
  qc_gram_weight: tenThousand,
  inspect_avg_useful_width: hundred,
  defect_weight: tenThousand,
  shrinkage_rate: hundred,
  number: hundred,
  average_weight: tenThousand,
  single_price: tenThousand,
  tax_rate: hundred,
  sale_tax_rate: hundred,
  yield: hundred,
  buoyant_weight_price: tenThousand,
  rm_total_cost_price: hundred,
  total_use_roll: hundred,
  use_roll: hundred,
  sum_stock_roll: hundred,
  sum_stock_weight: tenThousand,
  sum_stock_length: tenThousand,
  weight_error: tenThousand,
  settle_weight: tenThousand,
  out_length: tenThousand,
  length_unit_price: tenThousand,
  settle_error_weight: tenThousand,
  arrange_roll: hundred,
  arrange_weight: tenThousand,
  arrange_length: tenThousand,
  change_roll: hundred,
  change_weight: tenThousand,
  change_length: tenThousand,
  result_roll: hundred,
  result_weight: tenThousand,
  result_length: tenThousand,
  base_unit_weight: tenThousand,
  change_piece_count: hundred,
  cost_price: tenThousand,
  rm_cost_price: hundred, // 原料金额
  sale_price: tenThousand,
  sale_settle_weight: tenThousand,
  sale_weight: tenThousand,
  sale_weight_error: tenThousand,
  supplier_weight: tenThousand,
  supplier_weight_error: tenThousand,
  total_sale_price: hundred,
  total_purchase_price: hundred,
  in_weight: tenThousand,
  length: tenThousand,
  in_roll: hundred,
  in_length: tenThousand,
  no_return_length: tenThousand,
  no_return_roll: hundred,
  no_return_weight: tenThousand,
  // upper_limit: hundred,
  // lower_limit: hundred,
  change_other_price: hundred,
  change_unit_price: tenThousand,
  change_total_price: hundred,
  change_use_yarn_quantity: tenThousand,
  num: hundred,
  inspect_avg_edge_width: hundred,
  inspect_avg_gram_weight: tenThousand,
  settle_price: hundred,
  total_should_collect_money: hundred,
  length_cut_sale_price: tenThousand,
  defect_position: tenThousand,
  remove_money: hundred,
  discount_price: hundred,
  chargeback_money: hundred,
  balance: hundred,
  actually_collect_price: hundred,
  last_balance_price: hundred,
  should_collect_money: hundred,
  collected_money: hundred,
  payed_money: hundred,
  should_pay_money: hundred,
  offset_price: hundred,
  end_period: hundred,
  grey_fabric_cost: tenThousand,
  return_price: tenThousand,
  length_cut_return_price: tenThousand,
  last_stock_roll: hundred,
  last_stock_weight: tenThousand,
  adjust_roll: hundred,
  adjust_weight: tenThousand,
  whole_piece_count_stock: hundred,
  bulk_piece_count_stock: hundred,
  total_weight_stock: tenThousand,
  whole_piece_count_diff: hundred,
  bulk_piece_count_diff: hundred,
  total_weight_diff: tenThousand,
  total_price_diff: hundred,
}

const dateTemp = 'YYYY-MM-DD'// 格式化为YYYY-MM-DD
// 字典，用于格式化日期
export const baseDateDict = {
  delivery_date: dateTemp,
  qc_date: dateTemp,
  receive_date: dateTemp,
  order_time: dateTemp,
  production_date: dateTemp,
}
