/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (app, vm) => {
	console.log('🔧 初始化 HTTP API...');

	// 获取全局属性
	const $u = app.config.globalProperties.$u;
	const store = app.config.globalProperties.$store;

	if (!$u) {
		console.warn('uView Plus 不可用，跳过 API 配置');
		return;
	}

	// 参数配置对象
	const config = store?.state?.vuex_config || {
		adminPath: '/admin' // 默认管理路径
	};

	console.log('📋 API 配置:', config);
	
	// 将各个定义的接口名称，统一放进对象挂载到$u.api下
	$u.api = {
		
		// 基础服务：登录登出、身份信息、菜单授权、切换系统、字典数据等
		lang: (params = {}) => $u.get('/lang/'+params.lang),
		index: (params = {}) => $u.get(config.adminPath+'/mobile/index', params),
		getUserInfo: (params = {}) => $u.get(config.adminPath+'/mobile/user/getUserInfo', params),
		login: (params = {}) => $u.post(config.adminPath+'/mobile/login/loginByPassword', params),
		// PDA登录接口
		pdaLogin: (params = {}) => $u.postJson('/login', params),
		pdaLogout: (params = {}) => $u.postJson('/logout', params),
		// 获取配布单列表
		getFpmArrangeOrderList: (params = {}) => $u.get('/product/fpmArrangeOrder/getFpmArrangeOrderList', params),
		// 获取成品配布单详情
		getFpmArrangeOrder: (params = {}) => $u.get('/product/fpmArrangeOrder/getFpmArrangeOrder', params),
		// 生成出仓单
		outFpmArrangeOrder: (params = {}) => $u.put('/product/fpmArrangeOrder/outFpmArrangeOrder', params),
		sendCode: (params = {}) => $u.post(config.adminPath+'/mobile/login/sendCode', params),
		registerUser: (params = {}) => $u.post(config.adminPath+'/mobile/user/registerUser', params),
		//首页相关api
		getIndexCardInfo: (params = {}) => $u.get(config.adminPath+'/mobile/index/getIndexCardInfo', params),
		getM2mOrderFlowList: (params = {}) => $u.get(config.adminPath+'/mobile/index/getM2mOrderFlowList', params),
		//获取卡可购买套餐包
		getM2mOrderPackageList: (params = {}) => $u.get(config.adminPath+'/mobile/index/getM2mOrderPackageList', params),
		
		logout: (params = {}) => $u.get(config.adminPath+'/mobile/login/logout', params),
		authInfo: (params = {}) => $u.get(config.adminPath+'/authInfo', params),
		menuTree: (params = {}) => $u.get(config.adminPath+'/menuTree', params),
		switchSys: (params = {}) => $u.get(config.adminPath+'/switch/'+params.sysCode),
		dictData: (params = {}) => $u.get(config.adminPath+'/system/dict/data/type/'+params.dictType),
		
		// 账号服务：验证码接口、忘记密码接口、注册账号接口等
		validCode: (params = {}) => $u.getText('/validCode', params),
		getFpValidCode: (params = {}) => $u.post('/account/getFpValidCode', params),
		savePwdByValidCode: (params = {}) => $u.post('/account/savePwdByValidCode', params),
		getRegValidCode: (params = {}) => $u.post('/account/getRegValidCode', params),
		saveRegByValidCode: (params = {}) => $u.post('/account/saveRegByValidCode', params),
		
		// APP公共服务
		upgradeCheck: () => $u.post('/app/upgrade/check', {appCode: config.appCode, appVersion: config.appVersion}),
		commentSave: (params = {}) => $u.post('/app/comment/save', params),
		
		// 个人信息修改
		user: {
			saveUserInfo: (params = {}) => $u.post(config.adminPath+'/mobile/user/saveUserInfo', params),
			infoSavePwd: (params = {}) => $u.put(config.adminPath+'/system/user/profile/updatePwd', params),
			infoSavePqa: (params = {}) => $u.post(config.adminPath+'/sys/user/infoSavePqa', params),
		},
		
		// 员工用户查询
		empUser: {
			listData: (params = {}) => $u.get(config.adminPath+'/sys/empUser/listData', params),
		},
		// 获取坯布其他出货单列表
		getGfmOtherDeliveryOrderList: (params = {}) => $u.get('/product/fpmArrangeOrder/getFpmArrangeOrderList', params),
		
		// 组织机构查询
		office: {
			treeData: (params = {}) => $u.get(config.adminPath+'/sys/office/treeData', params),
		},
		
		// 增删改查例子
		testData: {
			form: (params = {}) => $u.post(config.adminPath+'/test/testData/form', params),
			list: (params = {}) => $u.post(config.adminPath+'/test/testData/listData', params),
			save: (params = {}) => $u.postJson(config.adminPath+'/test/testData/save', params),
			disable: (params = {}) => $u.post(config.adminPath+'/test/testData/disable', params),
			enable: (params = {}) => $u.post(config.adminPath+'/test/testData/enable', params),
			delete: (params = {}) => $u.post(config.adminPath+'/test/testData/delete', params),
		},
		
	};
	
}

export default {
	install
}
