/**
 * 最小化 HTTP 拦截器 - 避免 setConfig 问题
 */

const install = (app, vm) => {
	console.log('🔧 初始化最小化 HTTP 拦截器...');
	
	// 检查 uView Plus 是否可用
	const $u = app.config.globalProperties.$u;
	if (!$u) {
		console.warn('uView Plus 不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	console.log('📦 uView Plus 可用:', !!$u);
	console.log('🌐 HTTP 模块可用:', !!$u.http);
	console.log('⚙️ setConfig 方法类型:', typeof $u.http?.setConfig);
	console.log('🔗 interceptor 对象:', typeof $u.http?.interceptor);
	
	if (!$u.http) {
		console.warn('$u.http 模块不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	// 获取 store 实例
	const store = app.config.globalProperties.$store;
	console.log('🗄️ Store 可用:', !!store);
	console.log('📡 API URL:', store?.state?.apiurl || '未配置');
	
	try {
		// 跳过 setConfig，直接配置拦截器
		console.log('⚠️ 跳过 setConfig 配置，直接设置拦截器...');
		
		// 检查拦截器是否可用
		if ($u.http.interceptor && typeof $u.http.interceptor === 'object') {
			console.log('🔗 配置请求拦截器...');
			
			// 请求拦截器
			$u.http.interceptor.request = (req) => {
				console.log('📤 请求拦截:', req.url);
				
				// 设置基础 URL（如果没有完整 URL）
				if (req.url && !req.url.startsWith('http')) {
					const baseUrl = store?.state?.apiurl || 'http://localhost:8980';
					req.url = baseUrl + (req.url.startsWith('/') ? '' : '/') + req.url;
					console.log('🌐 完整 URL:', req.url);
				}
				
				// 设置默认头部
				if (!req.header) {
					req.header = {};
				}
				
				// 添加平台标识
				if (!req.header['Platform']) {
					req.header['Platform'] = 2;
				}
				
				// 添加请求头
				if (!req.header['x-requested-with']) {
					req.header['x-requested-with'] = 'XMLHttpRequest';
				}
				
				// 添加 Token
				const token = store?.state?.vuex_token || '';
				if (token && !req.header['Authorization']) {
					req.header['Authorization'] = token;
					console.log('🔑 添加 Token');
				}
				
				// 添加记住我
				const remember = store?.state?.vuex_remember || '';
				if (remember && req.remember && !req.header['x-remember']) {
					req.header['x-remember'] = remember;
					req.remember = false;
					console.log('💾 添加记住我');
				}
				
				console.log('📤 最终请求:', {
					url: req.url,
					method: req.method,
					header: req.header
				});
				
				return req;
			};
			
			// 响应拦截器
			$u.http.interceptor.response = async (res, req) => {
				console.log('📥 响应拦截:', res.statusCode, req.url);
				
				// 处理 401 未授权
				if (res.statusCode === 401) {
					console.log('🚫 登录已过期');
					
					// 显示提示
					if ($u.toast) {
						$u.toast('登录已过期，请重新登录');
					} else {
						uni.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
					}
					
					// 清除 token
					uni.removeStorageSync('token');
					if ($u.vuex) {
						$u.vuex('vuex_token', '');
						$u.vuex('vuex_user', {});
					}
					
					// 跳转到登录页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/sys/login/index'
						});
					}, 1500);
					
					return false;
				}
				
				// 处理服务器错误
				if (!res.data) {
					console.log('❌ 服务器无响应');
					if ($u.toast) {
						$u.toast('未连接到服务器');
					} else {
						uni.showToast({ title: '未连接到服务器', icon: 'none' });
					}
					return Promise.reject(new Error('未连接到服务器'));
				}
				
				// 处理业务错误
				if (res.data.code !== undefined && res.data.code !== 0) {
					console.log('❌ 业务错误:', res.data.msg);
					if ($u.toast) {
						$u.toast(res.data.msg || '请求失败');
					} else {
						uni.showToast({ title: res.data.msg || '请求失败', icon: 'none' });
					}
					return Promise.reject(new Error(res.data.msg || '请求失败'));
				}
				
				// 处理 token 更新
				if (res.data && typeof res.data === 'object' && res.data.token) {
					console.log('🔄 更新 token:', res.data.token);
					if ($u.vuex) {
						$u.vuex('vuex_token', res.data.token);
						if (res.data.user) {
							$u.vuex('vuex_user', res.data.user);
						}
					}
				}
				
				// 处理记住我
				if (res.header && res.header['x-remember']) {
					const remember = res.header['x-remember'];
					if (remember && remember !== 'deleteMe') {
						if ($u.vuex) {
							$u.vuex('vuex_remember', remember);
						}
					} else {
						if ($u.vuex) {
							$u.vuex('vuex_remember', '');
						}
					}
				}
				
				console.log('✅ 请求成功');
				return res.data?.data || res.data || res;
			};
			
			console.log('✅ 拦截器配置成功');
		} else {
			console.warn('⚠️ 拦截器不可用，类型:', typeof $u.http.interceptor);
		}
		
		// 添加便捷方法（不依赖 setConfig）
		if ($u && $u.http) {
			// GET 文本请求
			$u.getText = (url, data = {}, header = {}) => {
				console.log('📄 GET 文本请求:', url);
				return $u.http.request({
					dataType: 'text',
					method: 'GET',
					url,
					header,
					data
				});
			};
			
			// POST JSON 请求
			$u.postJson = (url, data = {}, header = {}) => {
				console.log('📝 POST JSON 请求:', url);
				header['content-type'] = 'application/json';
				return $u.http.request({
					url,
					method: 'POST',
					header,
					data
				});
			};
			
			console.log('✅ 便捷方法添加成功');
		}
		
	} catch (error) {
		console.error('❌ HTTP 拦截器配置失败:', error);
		console.error('错误详情:', error.message);
		console.error('错误堆栈:', error.stack);
		
		// 即使失败也不阻止应用启动
		console.log('⚠️ 继续启动应用，但 HTTP 拦截器功能可能不完整');
	}
	
	console.log('🎉 HTTP 拦截器初始化完成（最小化版本）');
}

export default {
	install
}
