/**
 * 简化版 HTTP 拦截器 - Vue 3 兼容
 */

const install = (app, vm) => {
	console.log('🔧 初始化简化版 HTTP 拦截器...');
	
	// 检查 uView Plus 是否可用
	const $u = app.config.globalProperties.$u;
	if (!$u) {
		console.warn('uView Plus 不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	console.log('📦 uView Plus 可用:', !!$u);
	console.log('🌐 HTTP 模块可用:', !!$u.http);
	
	if (!$u.http) {
		console.warn('$u.http 模块不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	// 获取 store 实例
	const store = app.config.globalProperties.$store;
	console.log('🗄️ Store 可用:', !!store);
	
	// 尝试配置 HTTP
	try {
		// 检查 setConfig 方法
		if (typeof $u.http.setConfig === 'function') {
			console.log('⚙️ 配置 HTTP 设置...');
			
			const config = {
				baseUrl: store ? store.state.apiurl : 'http://localhost:8980',
				originalData: true,
				header: {
					'Platform': 2,
					'content-type': 'application/x-www-form-urlencoded',
					'x-requested-with': 'XMLHttpRequest'
				}
			};
			
			console.log('📡 HTTP 配置:', config);
			$u.http.setConfig(config);
			console.log('✅ HTTP 配置成功');
		} else {
			console.warn('⚠️ setConfig 方法不可用，类型:', typeof $u.http.setConfig);
		}
		
		// 检查拦截器
		if ($u.http.interceptor && typeof $u.http.interceptor === 'object') {
			console.log('🔗 配置请求拦截器...');
			
			// 请求拦截器
			$u.http.interceptor.request = (req) => {
				console.log('📤 请求拦截:', req.url);
				
				// 添加 Token
				const token = store ? store.state.vuex_token : '';
				if (token && !req.header['Authorization']) {
					req.header['Authorization'] = token;
					console.log('🔑 添加 Token');
				}
				
				return req;
			};
			
			// 响应拦截器
			$u.http.interceptor.response = (res, req) => {
				console.log('📥 响应拦截:', res.statusCode);
				
				// 处理 401 未授权
				if (res.statusCode === 401) {
					console.log('🚫 登录已过期');
					if ($u.toast) {
						$u.toast('登录已过期，请重新登录');
					} else {
						uni.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
					}
					
					// 清除 token
					uni.removeStorageSync('token');
					if ($u.vuex) {
						$u.vuex('vuex_token', '');
						$u.vuex('vuex_user', {});
					}
					
					// 跳转到登录页
					uni.reLaunch({
						url: '/pages/sys/login/index'
					});
					return false;
				}
				
				// 处理服务器错误
				if (!res.data) {
					console.log('❌ 服务器无响应');
					if ($u.toast) {
						$u.toast('未连接到服务器');
					} else {
						uni.showToast({ title: '未连接到服务器', icon: 'none' });
					}
					return Promise.reject(new Error('未连接到服务器'));
				}
				
				// 处理业务错误
				if (res.data.code !== 0) {
					console.log('❌ 业务错误:', res.data.msg);
					if ($u.toast) {
						$u.toast(res.data.msg);
					} else {
						uni.showToast({ title: res.data.msg, icon: 'none' });
					}
					return Promise.reject(new Error(res.data.msg));
				}
				
				console.log('✅ 请求成功');
				return res.data.data || res.data;
			};
			
			console.log('✅ 拦截器配置成功');
		} else {
			console.warn('⚠️ 拦截器不可用');
		}
		
		// 添加便捷方法
		if ($u) {
			// GET 文本请求
			$u.getText = (url, data = {}, header = {}) => {
				console.log('📄 GET 文本请求:', url);
				return $u.http.request({
					dataType: 'text',
					method: 'GET',
					url,
					header,
					data
				});
			};
			
			// POST JSON 请求
			$u.postJson = (url, data = {}, header = {}) => {
				console.log('📝 POST JSON 请求:', url);
				header['content-type'] = 'application/json';
				return $u.http.request({
					url,
					method: 'POST',
					header,
					data
				});
			};
			
			console.log('✅ 便捷方法添加成功');
		}
		
	} catch (error) {
		console.error('❌ HTTP 拦截器配置失败:', error);
		console.error('错误详情:', error.message);
		console.error('错误堆栈:', error.stack);
	}
	
	console.log('🎉 HTTP 拦截器初始化完成');
}

export default {
	install
}
