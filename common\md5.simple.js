/**
 * 简化版 MD5 - ES6 模块兼容
 * 基于原始 js-md5 库，但移除了 Node.js 特定代码
 */

// 导入原始的 md5 实现
import './md5.js';

// 获取全局的 md5 函数
let md5Function;

if (typeof window !== 'undefined' && window.md5) {
  // 浏览器环境
  md5Function = window.md5;
} else {
  // 如果没有找到，创建一个基本实现
  md5Function = function(input) {
    console.warn('MD5 function not available, returning empty string');
    return '';
  };
}

// ES6 默认导出
export default md5Function;
