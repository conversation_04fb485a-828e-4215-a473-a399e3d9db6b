/**
 * 通用公共js库，主要定义api接口，auth apikey 常量值和通用函数
 */
import store from '@/store/index.js';
 
// var apiurl = 'http://192.168.1.66:50002/hcscm/pda/v1/';  //开发环境 添
// var apiurl = 'http://192.168.1.55:50002/hcscm/pda/v1/';  //开发环境 添
//var apiurl ='http://192.168.1.127:50003/hcscm/pda/v1/'; //开发环境 添
// var apiurl = 'https://hcscmpre.zzfzyc.com/hcscm/pda/v1/';  //预发布环境
// var apiurl = 'https://hcscmtest.zzfzyc.com/hcscm/pda/v1/'; //测试环境
//var apiurl = 'https://hcscmtest.zzfzyc.com/hcscm/'; //ERP测试环境

export const getApiUrl = () => store.state.apiurl

export function updateApiUrl(newUrl) {
	store.dispatch('updateApiUrl', newUrl);
}
// 添加统一请求方法
export function request(options) {
  const token = uni.getStorageSync('RemoteTokenData')?.token;
  const apiurl = getApiUrl()
  return uni.request({
    url: apiurl + options.url,
    method: options.method || 'GET',
    data: options.data,
    header: {
      'Platform': 2,
      'Authorization': token,
      ...(options.header || {})
    },
	success: options.success,
	fail: options.fail,
	complete: options.complete
  });
}
// 设置标识 app wx 
var shebei = '';
//#ifdef APP-PLUS
shebei = 'app';
//#endif

//#ifdef MP-WEIXIN
shebei = 'wx';
//#endif

const S4 = function() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
};
export function guid() {
  return (S4() + S4() +'-' +S4() +'-' +S4() +'-' +S4() +'-' +S4() +S4() +S4()
  );
}

// store 解析字符串或对象
export function parGetData(v) {
	if (v.indexOf('obj-') === 0) {
		v = v.slice(4);
		return JSON.parse(v);
	} else {
		if (v.indexOf('str-') === 0) {
			return v.slice(4);
		}
	}
}
// localstore 存储字符串或对象
export function parSetData(v) {
	if (typeof v == 'object') {
		v = JSON.stringify(v);
		v = 'obj-' + v;
	} else {
		v = 'str-' + v;
	}
	return v;
}

function getPrefixNumber(maybeNumStr) {
	const numReg = /^[0-9]*[!0-9]*/;
	if (numReg.test(maybeNumStr)) {
		return maybeNumStr.match(numReg)[0];
	}
}

export function parFabricGoodsBarCode2D(aSourceBarCode) {
	let aOjbectBarCode = aSourceBarCode.toString();
	console.log(aOjbectBarCode);
	var aCrockNo = '';
	var aGoodsBillNo = '';
	var aFabricGoodsNo = '';
	var aGoodsCodeNo = '';
	var aGoodsCodeName = '';
	var aQty = '';
	var aBarCodeDig = '';
	console.log("****************");
	if (aOjbectBarCode.indexOf('|') > 0) {
		console.log("aOjbectBarCode--&8888888888->");
		var aBarCodeList = aOjbectBarCode.split('|');
		aCrockNo = aBarCodeList[3];
		aGoodsBillNo = aBarCodeList[5];
		aFabricGoodsNo = aBarCodeList[1];
		aGoodsCodeNo = aBarCodeList[2];
		aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
		if (aGoodsCodeNo.length == 1) {
			aGoodsCodeNo = '00' + aGoodsCodeNo;
		} else if (aGoodsCodeNo.length == 2) {
			aGoodsCodeNo = '0' + aGoodsCodeNo;
		};
		aQty = aBarCodeList[6];
		aBarCodeDig = aBarCodeList[0];
	} else if (aOjbectBarCode.indexOf('$') > 0) {
		console.log("aOjbectBarCode--&6555555->");
		aOjbectBarCode = aOjbectBarCode.replace('￥', '$');
		aOjbectBarCode = aOjbectBarCode.replace('＃', '#');
		aOjbectBarCode = aOjbectBarCode.replace('##', '#');
		aOjbectBarCode = aOjbectBarCode.replace('^', '$');
		var aBarCodeList = aOjbectBarCode.split('$');

		console.log("aBarCodeList.length---->" + aBarCodeList.length);
		if (aBarCodeList.length <= 5) {
			aCrockNo = aBarCodeList[0];
			aGoodsBillNo = aBarCodeList[1];
			aFabricGoodsNo = aBarCodeList[2];
			aGoodsCodeNo = aBarCodeList[3];
			console.log("aGoodsCodeNo---->" + aGoodsCodeNo);

			//aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
			if (aGoodsCodeNo == '') {
				aGoodsCodeNo = aBarCodeList[3];
			}
			aGoodsCodeName = "";
			aQty = aBarCodeList[4];
			aBarCodeDig = aBarCodeList[1];
		} else {
			aCrockNo = aBarCodeList[0];
			aGoodsBillNo = aBarCodeList[1];
			aFabricGoodsNo = aBarCodeList[2];
			aGoodsCodeNo = aBarCodeList[4];
			console.log("aGoodsCodeNo1111---->" + aGoodsCodeNo);
			//aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
			console.log("aGoodsCodeNo222---->" + aGoodsCodeNo);
			if (aGoodsCodeNo == '') {
				aGoodsCodeNo = aBarCodeList[4];
			}
			console.log("aGoodsCodeNo33333333333---->" + aGoodsCodeNo);
			aGoodsCodeName = aBarCodeList[5];
			aQty = aBarCodeList[6];
			aBarCodeDig = aBarCodeList[7];
		}
	} else if (aOjbectBarCode.indexOf('^') > 0) {
		var aBarCodeList = aOjbectBarCode.split('^');
		console.log(">>>aBarCodeList.length>>>>" + aBarCodeList.length.toString());
		if (aBarCodeList.length > 7) {
			if (aBarCodeList[0] == '99' || aBarCodeList[0] == '96') {
				aCrockNo = aBarCodeList[3];
				aGoodsBillNo = aBarCodeList[4];
				aFabricGoodsNo = aBarCodeList[5];
				aGoodsCodeNo = aBarCodeList[6];
				aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
				aGoodsCodeName = aBarCodeList[6];
				aQty = aBarCodeList[7];
				aBarCodeDig = aBarCodeList[4];
			} else {
				aCrockNo = aBarCodeList[1];
				aGoodsBillNo = aBarCodeList[3];
				aFabricGoodsNo = aBarCodeList[5].replace("ZH", "");
				aGoodsCodeNo = aBarCodeList[7];
				aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
				aGoodsCodeName = aBarCodeList[7];
				aQty = aBarCodeList[8];
				aBarCodeDig = aBarCodeList[4];
			}
		} else {
			aCrockNo = aBarCodeList[0];
			aGoodsBillNo = aBarCodeList[1];
			aFabricGoodsNo = aBarCodeList[2];
			aGoodsCodeNo = aBarCodeList[4];
			aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
			aGoodsCodeName = aBarCodeList[5];
			aQty = aBarCodeList[6];
			aBarCodeDig = aBarCodeList[7];
		}
	} else if (aOjbectBarCode.indexOf('^') == 0) {
		var aBarCodeList = aOjbectBarCode.split('^');
		aCrockNo = aBarCodeList[1];
		aGoodsBillNo = aBarCodeList[2];
		aFabricGoodsNo = aBarCodeList[3];
		aGoodsCodeNo = '';
		//aGoodsCodeNo = getPrefixNumber(aGoodsCodeNo);
		//aGoodsCodeName = aBarCodeList[5];
		aQty = aBarCodeList[5];
		console.log(">>>>>>>>" + aCrockNo + ">>>" + aGoodsBillNo.toString() + ">>>>" + aQty.toString());
		aBarCodeDig = 0;
	} else if (aOjbectBarCode.indexOf('http') > 0) {
		console.log("aOjbectBarCode--&&&->");
		return '';
	} else {
		aCrockNo = '';
		aGoodsBillNo = '';
		aFabricGoodsNo = '';
		aGoodsCodeNo = '';
		aGoodsCodeNo = '';
		aGoodsCodeName = '';
		aQty = 0;
		aBarCodeDig = aSourceBarCode;
		console.log("aOjbectBarCode--&66666->"+aBarCodeDig) ;
		//return '';
	}
	/*
	if (aGoodsCodeNo.length == 1) {
		aGoodsCodeNo = '00' + aGoodsCodeNo;
	} else if (aGoodsCodeNo.length == 2) {
		aGoodsCodeNo = '0' + aGoodsCodeNo;
	};
	*/

	return aCrockNo + ',' + aGoodsBillNo + ',' + aBarCodeDig + ',' + aFabricGoodsNo + ',' + aGoodsCodeNo + ',' + aQty;
}

export function GetGoodsQRBarCode(aSourceQRBarCode) {
	let aQRBarCode = aSourceQRBarCode.toString().trimRight().trimLeft();
	aQRBarCode = aQRBarCode.replace('￥', '$');
	aQRBarCode = aQRBarCode.replace('＃', '#');
	aQRBarCode = aQRBarCode.replace('##', '#');
	aQRBarCode = aQRBarCode.replace('^', '$');
	
	if (aQRBarCode.indexOf('$') > 0)
	{
		var aQRBarCodeList = aQRBarCode.split('$');
		var aCrockNo = aQRBarCodeList[0];
		var aGoodsBillNo = aQRBarCodeList[1];
		var aFabricGoodsNo = aQRBarCodeList[2];
		var aSaleOrderNo = aQRBarCodeList[3];
		var aFabricGoodsName = '';
		var aGoodsCodeNo = aQRBarCodeList[4].split('#')[0];
		var aGoodsCodeName = aQRBarCodeList[4].split('#')[1];
		var aQty = aQRBarCodeList[5];
		var aBarCodeDig= '';
		
		return aCrockNo + '^' + aGoodsBillNo + '^' + aBarCodeDig + '^' 
			+ aFabricGoodsNo + '^' + aFabricGoodsName + '^' + aGoodsCodeNo 
			+ '^' + aGoodsCodeName +'^' + aSaleOrderNo +'^'+ aQty+'^';
	} else if (aQRBarCode.indexOf('http') > 0) {
		return '';
	} else {
		return '';
	}
}

export function parYarnGoodsBarCode2D(aSourceBarCode) {
	aSourceBarCode = aSourceBarCode.replace('\n','');
	aSourceBarCode = aSourceBarCode.replace('\t','');
	aSourceBarCode = aSourceBarCode.replace('\s','');
	aSourceBarCode = aSourceBarCode.replace('\r','');
	let aOjbectBarCode = aSourceBarCode.toString();
	var aDyeWorksDigCode = '';
	var aCustomerPO = '';
	var aYarnGoodsCodeNo = '';
	var aYarnGoodsColorNo = '';
	var aYarnDyeOrderDate = '';
	var aYarnCrockNo = '';
	var aYarnBoxBillNo = '';
	var aYarnBoxRoll = '';
	var aYarnGrossQty = 0;
	var aYarnNetQty = 0;
	if (aOjbectBarCode.indexOf('^') >= 0) {
		var aBarCodeList = aOjbectBarCode.split('^');
		aDyeWorksDigCode = aBarCodeList[1];
		aCustomerPO = aBarCodeList[2];
		aYarnGoodsCodeNo = aBarCodeList[3];
		aYarnGoodsColorNo = aBarCodeList[4];
		aYarnDyeOrderDate = aBarCodeList[5];
		aYarnCrockNo = aBarCodeList[6];
		aYarnBoxBillNo = aBarCodeList[7];
		aYarnBoxRoll = aBarCodeList[8];
		aYarnGrossQty = aBarCodeList[9];
		aYarnNetQty = aBarCodeList[10];
	} else if (aOjbectBarCode.indexOf('http') > 0) {
		console.log("aOjbectBarCode-CCCC-&&&->");
		return '';
	} else {
		aDyeWorksDigCode = '';
		aCustomerPO = '';
		aYarnGoodsCodeNo = '';
		aYarnGoodsColorNo = '';
		aYarnDyeOrderDate = '';
		aYarnCrockNo = '';
		aYarnBoxBillNo = '';
		aYarnBoxRoll = '';
		aYarnGrossQty = '';
		aYarnNetQty = '';
		//return '';
	}
	return aDyeWorksDigCode + ',' + aCustomerPO + ',' + aYarnGoodsCodeNo + ',' 
	+ aYarnGoodsColorNo + ',' + aYarnDyeOrderDate + ',' + aYarnCrockNo + ',' 
	+ aYarnBoxBillNo + ',' + aYarnBoxRoll + ',' + aYarnGrossQty + ',' + aYarnNetQty;
}

// 将对象中项目存到数组arr中
function concat(arr, obj) {
	Object.keys(obj).forEach(function(key) {
		arr.push(obj[key])
	});
	return arr;
}

// 新建一个audio实例
function createAudio(src) {
	//console.log("createInnerAudioContext");
	let innerAudioContext = uni.createInnerAudioContext();
	innerAudioContext.autoplay = true;
	innerAudioContext.src = src;
	innerAudioContext.onPlay(() => {
		console.log("开始播放");
	});

	innerAudioContext.onEnded(() => {
		//console.log('i am onEnded')
		//播放结束，销毁该实例
		//innerAudioContext.destroy();
		console.log('已执行destory()')
	});
	innerAudioContext.onError(res => {
		//console.log(res.errMsg);
		//console.log(res.errCode);
		innerAudioContext.stop();
	});
	return innerAudioContext;
}

let successAudio = undefined;
let errorAudio = undefined;

function playSuccessAudio() {
	if (!successAudio) {
		//console.log("create success");
		successAudio = createAudio('/static/audio/appright.wav');
		successAudio.onError(res => {
			console.log(res.errMsg);
			console.log(res.errCode);
			console.log(res);
			successAudio.destroy();
			successAudio = undefined; //createAudio('/static/audio/appright.wav');

		});
	}
	successAudio.stop();
	successAudio.play();
}

function playErrorAudio() {
	if (!errorAudio) {
		//console.log("create error");
		errorAudio = createAudio('/static/audio/apperror.wav');
		errorAudio.onError(res => {
			console.log(res.errMsg);
			console.log(res.errCode);
			console.log(res);
			errorAudio.destroy();
			errorAudio = undefined; //createAudio('/static/audio/apperror.wav');
		})
	}
	console.log("create error");
	errorAudio.stop();
	errorAudio.play();
}


export default {
	get apiurl() {
		return getApiUrl()
	},
	request,
	shebei,
	updateApiUrl,
	parSetData,
	parGetData,
	playSuccessAudio,
	playErrorAudio,
};
