<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">出仓单号：{{item.BillNo}}</view>
		<view class="cardRow">
			<view>出仓类型：</view>
			<view>{{item.BillTypeName}}</view>
		</view>
		<view class="cardRow">
			<view>出仓日期：</view>
			<view>{{item.BillDate}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{item.StoreName}}</view>
		</view>
		<view class="cardRow">
			<view>往来单位：</view>
			<view>{{item.CustomerName}}</view>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{item.OpName}} {{item.OpDate}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{item.CommitName}} {{item.CommitDate}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>	
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			onlaunch(e) {
				console.log("---->>>" + item.OpDate);
			},
				
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				uni.$bjInfo = this.item;
				uni.navigateTo({
					url: '/pages/saleship/detail?index=' + this.index
				})
			}
		}
	}
</script>

<style>
	
</style>

