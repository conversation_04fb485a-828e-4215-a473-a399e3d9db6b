<template>
  <view class="navbar_container">
    <view class="navbar_content" :style="navbarStyle">
      <!-- 左侧返回按钮 -->
      <view class="navbar_left" @click="handleLeftClick">
        <slot name="left">
          <text v-if="showBack" class="iconfont icon-arrow-left" :style="{ color: iconColor }">返回</text>
        </slot>
      </view>

      <!-- 中间标题 -->
      <view class="navbar_center">
        <slot name="center">
          <text :style="titleStyle">{{ title }}</text>
        </slot>
      </view>

      <!-- 右侧自定义区域 -->
      <view class="navbar_right" @click="handleRightClick">
        <slot name="right"></slot>
      </view>
    </view>
    <view class="safe_area" :style="safeAreaStyle"></view>
  </view>
</template>

<script>
import { getStatusBarHeight } from "@/utils/safeArea";
export default {
  props: {
    // 标题文字
    title: {
      type: String,
      default: ''
    },
    // 标题颜色
    titleColor: {
      type: String,
      default: '#333333'
    },
    // 标题大小
    titleSize: {
      type: Number,
      default: 18
    },
    // 标题粗细
    titleWeight: {
      type: [Number, String],
      default: 600
    },
    // 图标颜色（如返回箭头）
    iconColor: {
      type: String,
      default: '#333333'
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },
    // 自定义返回逻辑
    customBack: {
      type: Function,
      default: null
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    // 是否显示阴影
    showShadow: {
      type: Boolean,
      default: true
    },
    // 自定义阴影样式
    shadowStyle: {
      type: String,
      default: 'none'
    }
  },
  
  data() {
    return {
      getStatusBarHeight: getStatusBarHeight(),
      contentHeight: 0,
    };
  },
  
  computed: {
    navbarStyle() {
      return {
        backgroundColor: this.backgroundColor,
        boxShadow: this.showShadow ? this.shadowStyle : 'none',
        top: `${this.getStatusBarHeight}px`
      }
    },
    
    titleStyle() {
      return {
        color: this.titleColor,
        fontSize: `${this.titleSize}px`,
        fontWeight: this.titleWeight
      }
    },
    
    safeAreaStyle() {
      const totalHeight = this.getStatusBarHeight + this.contentHeight;
      return {
        height: `${totalHeight}px`,
        backgroundColor: this.backgroundColor
      }
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      const query = uni.createSelectorQuery().in(this);
      query.select('.navbar_content').boundingClientRect(data => {
        if (data) {
          this.contentHeight = data.height;
        }
      }).exec();
    });
  },

  methods: {
    handleLeftClick() {
      if (this.customBack) {
        this.customBack();
      } else if (this.showBack) {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          uni.navigateBack({
            delta: 1
          });
        }
      }
      this.$emit('leftClick');
    },

    handleRightClick() {
      this.$emit('rightClick');
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar_container {
  z-index: 999;
  .navbar_content {
    position: fixed;
    left: 0;
    right: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 12px;
    
    .navbar_left {
      min-width: 60px;
      .icon-arrow-left {
        font-size: 16px;
      }
    }
    
    .navbar_center {
      flex: 1;
      text-align: center;
      // 防止标题过长
      padding: 0 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .navbar_right {
      min-width: 60px;
      text-align: right;
	  font-size: 16px;
    }
  }
  
  .safe_area {
    width: 100%;
  }
}
</style>
