# ExpandableForm 可展开表单组件

一个具有动态伸缩效果的表单组件，支持平滑的展开/收起动画。

## 特性

- ✅ 平滑的展开/收起动画
- ✅ 按钮位置动态调整
- ✅ 可自定义标题和按钮文字
- ✅ 支持插槽内容
- ✅ 移动端优化

## 使用方法

### 基本用法

```vue
<template>
  <expandable-form title="基础信息">
    <template #basic-fields>
      <!-- 基础字段内容 -->
      <view class="form-item">
        <text class="form-label">字段1</text>
        <view class="form-value">
          <text class="value-text">值1</text>
        </view>
      </view>
    </template>

    <template #expanded-fields>
      <!-- 展开后的字段内容 -->
      <view class="form-item">
        <text class="form-label">展开字段1</text>
        <view class="form-value">
          <text class="value-text">展开值1</text>
        </view>
      </view>
    </template>
  </expandable-form>
</template>

<script>
import expandableForm from '@/components/expandable-form/expandable-form.vue'

export default {
  components: {
    expandableForm
  }
}
</script>
```

### 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '基础信息' | 表单标题 |
| expandText | String | '更多' | 展开按钮文字 |
| collapseText | String | '收起' | 收起按钮文字 |
| showExpandButton | Boolean | true | 是否显示展开按钮 |

### 插槽

| 插槽名 | 说明 |
|--------|------|
| basic-fields | 基础字段内容 |
| expanded-fields | 展开后的字段内容 |

### 动画效果

组件使用CSS transition实现平滑的展开/收起动画：
- 展开时：从上到下滑入，带有透明度变化
- 收起时：从下到上滑出，带有透明度变化
- 按钮位置会随着内容的展开/收起动态调整

### 样式自定义

组件提供了深度选择器支持，可以自定义表单项样式：

```scss
:deep(.form-item) {
  // 自定义表单项样式
}

:deep(.form-label) {
  // 自定义标签样式
}

:deep(.form-value) {
  // 自定义值样式
}
```
