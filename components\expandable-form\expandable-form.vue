<script setup>
import { ref, computed } from 'vue'

// 定义 props
const props = withDefaults(defineProps({
  title: String,
  expandText: String,
  collapseText: String,
  showExpandButton: Boolean
}), {
  title: '基础信息',
  expandText: '更多',
  collapseText: '收起',
  showExpandButton: true
})

// 响应式数据
const isExpanded = ref(false)

// 计算属性
const expandButtonText = computed(() => {
  return isExpanded.value ? props.collapseText : props.expandText
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<template>
  <view class="expandable-form">
    <view class="section-header">
      <text class="section-title">{{ title }}</text>
    </view>

    <view class="form-container">
      <!-- 基础字段插槽 -->
      <slot name="basic-fields"></slot>

      <!-- 展开按钮 -->
      <view
        v-if="showExpandButton"
        class="expand-btn"
        @click="toggleExpanded"
      >
        <text class="expand-text">{{ expandButtonText }}</text>
        <text class="expand-icon" :class="{ rotated: isExpanded }">></text>
      </view>

      <!-- 展开内容区域 -->
      <transition name="expand">
        <view v-if="isExpanded" class="expanded-content">
          <slot name="expanded-fields"></slot>
        </view>
      </transition>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.expandable-form {
  background-color: #FFFFFF;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.form-container {
  padding: 0 32rpx 32rpx;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx 0;
  margin: 16rpx 0 0 0;
  border-top: 1rpx solid #F0F0F0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #F8F8F8;
  }
}

.expand-text {
  font-size: 28rpx;
  color: #666666;
  transition: color 0.3s ease;
}

.expand-icon {
  font-size: 24rpx;
  color: #999999;
  transition: transform 0.3s ease;
  
  &.rotated {
    transform: rotate(90deg);
  }
}

.expanded-content {
  border-top: 1rpx solid #F0F0F0;
  margin-top: 16rpx;
  padding-top: 16rpx;
}

/* 展开/收起动画 */
.expand-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.expand-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
  overflow: hidden;
}

.expand-enter {
  opacity: 0;
  max-height: 0;
  transform: translateY(-20rpx);
}

.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-20rpx);
}

.expand-enter-to,
.expand-leave {
  opacity: 1;
  max-height: 800rpx;
  transform: translateY(0);
}

/* 表单项样式 */
:deep(.form-item) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.form-item-textarea {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
}

:deep(.form-label) {
  font-size: 28rpx;
  color: #333333;
  min-width: 160rpx;
}

:deep(.form-value) {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  justify-content: flex-end;
}

:deep(.value-text) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.placeholder-text) {
  font-size: 28rpx;
  color: #999999;
}

:deep(.arrow-icon) {
  font-size: 24rpx;
  color: #CCCCCC;
}

:deep(.form-textarea) {
  width: 100%;
  min-height: 120rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  border: none;
  resize: none;
}

:deep(.form-input) {
  width: 100%;
  height: 80rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  border: none;
}
</style>
