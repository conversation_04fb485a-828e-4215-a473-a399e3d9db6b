<template>
	<view>
		<image class="lxIcon" @click.stop="makePhoneCallFun" src="/static/icon/dianhua.png" mode="aspectFill">
		</image>
		<image class="lxIcon" @click.stop="sendMsgFun" src="/static/icon/duanxin.png" mode="aspectFill"></image>
		<!-- <image class="lxIcon" @click="sendEmailFun" src="/static/icon/youjian.png" mode="aspectFill"></image> -->
	</view>
</template>

<script>
	export default {
		props: {
			phone: ''
		},
		methods: {
			sendMsgFun: function() {
				if(!this.phone) {
					uni.$myModalFun('该客户暂未设置联系方式！')
					return
				}
				// #ifdef APP-PLUS
				var msg = plus.messaging.createMessage(plus.messaging.TYPE_SMS);
				msg.to = [this.phone];
				msg.body = '';
				plus.messaging.sendMessage(msg);
				// #endif
			},
			sendEmailFun: function() {
				// #ifdef APP-PLUS
				var msg = plus.messaging.createMessage(plus.messaging.TYPE_EMAIL);
				msg.to = ['<EMAIL>', '<EMAIL>'];
				msg.cc = ['<EMAIL>', '<EMAIL>'];
				msg.bcc = ['<EMAIL>', '<EMAIL>'];
				msg.subject = '测试邮件';
				msg.body = 'This is Pandora example test message';
				plus.messaging.sendMessage(msg, function() {
					alert("Send success!");
				}, function() {
					alert("Send failed!");
				});
				// #endif
			},
			makePhoneCallFun: function() {
				if(!this.phone) {
					uni.$myModalFun('该客户暂未设置联系方式！')
					return
				}
				// #ifdef APP-PLUS
				uni.makePhoneCall({
					phoneNumber: this.phone
				})
				// #endif
			}
		}
	}
</script>

<style>
	.lxIcon {
		width: 52rpx;
		height: 52rpx;
		margin-right: 26rpx;
	}
</style>
