.loader-one {
	width: 50rpx;
	height: 50rpx;
	position: relative;
	-webkit-animation: loading-one 1s infinite linear;
	animation: loading-one 1s infinite linear;
}

.loader-one,
.loader-one:after {
	border-radius: 50%;
}

@-webkit-keyframes loading-one {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes loading-one {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}