/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
import { createSSRApp } from 'vue';
import App from './App';

// 全局存储 vuex 的封装
import store from '@/store';

// 引入全局 uView Plus 框架
import uviewPlus from 'uview-plus';

// Vue i18n 国际化
import { createI18n } from 'vue-i18n';

// i18n 部分的配置，引入语言包，注意路径
import lang_zh_CN from '@/common/locales/zh_CN.js';
import lang_en from '@/common/locales/en.js';

// http 拦截器 - 使用备用版本（interceptor 对象不可用时）
import httpInterceptor from '@/common/http.interceptor.fallback.js';

// http 接口 API 抽离
import httpApi from '@/common/http.api.js';

// 工具函数
import { replaceAll } from '@/common/common.js';

export function createApp() {
	const app = createSSRApp(App);

	// 使用 store
	app.use(store);

	// 使用 uView Plus UI 框架
	app.use(uviewPlus, () => {
		return {
			options: {
				// 修改$u.config对象的属性
				config: {
					// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
					unit: 'rpx'
				}
			}
		}
	});

	// 配置 i18n
	const i18n = createI18n({
		legacy: true, // 使用 Legacy API 模式以支持 $t 函数
		locale: uni.getLocale() || 'zh_CN', // 获取已设置的语言，默认中文
		fallbackLocale: 'zh_CN', // 回退语言改为中文
		messages: {
			'zh_CN': lang_zh_CN,
			'en': lang_en,
		},
		// 添加全局属性配置
		globalInjection: true
	});
	app.use(i18n);

	// 全局属性
	app.config.globalProperties.replaceAll = replaceAll;

	// 使用 http 拦截器
	app.use(httpInterceptor);

	// 使用 http API
	app.use(httpApi);

	// 引入 uView Plus 提供的对 vuex 的简写法文件
	try {
		let vuexStore = require('@/store/$u.mixin.js');
		if (vuexStore && vuexStore.default) {
			app.mixin(vuexStore.default);
		}
	} catch (error) {
		console.warn('vuex mixin not found:', error);
	}

	// 引入 uView Plus 对小程序分享的 mixin 封装
	try {
		let mpShare = require('uview-plus/libs/mixin/mpShare.js');
		if (mpShare && mpShare.default) {
			app.mixin(mpShare.default);
		}
	} catch (error) {
		console.warn('mpShare mixin not found:', error);
	}

	return { app };
}
