<template>
	<view>
		<scroll-view scroll-y="true" :style="{height: scrollHeight}" @scrolltolower="" refresher-enabled
			:refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="" @refresherrestore="">
			<view v-if="list.length > 0">
				<view v-for="(item, index) in list" :key="index" @click="CardClickFun(item)">
					<BillBusinessCard :item="item" :isSelect="isSelect" :index="index"  @cxGetDataFun=""> </BillBusinessCard>
				</view>
				<getMore :isMore="isMore" nullMsg="已加载全部~"></getMore>
				<view class="h200"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	let that = this;
	import dataNull from '@/components/dataNull/dataNull.vue'
	import BillBusinessCard from '../../../components/card/baseDataBillBusinessList.vue'
	import util from '../../../common/util.js';
	export default {
		components: {
			dataNull,
			BillBusinessCard
		},
		data() {
			return {
				triggered: false,
				TypeStatus: 0,
				TypeNo: '',
				isMore: true,
				pageIndex: 1,
				scrollHeight: '667px',
				pageType: '',
				isSelect: false
			}
		},
		onLoad(e) {
			that = this;
			let khsxList = JSON.stringify(khsxData);
			that.sxList = JSON.parse(khsxList);
			let obj = {};

			this.CustomerTypes = e.type;
			this.pageType = e.type;
			console.log(">>>" + e.type);
			if (e.type) {
				that.isSelect = true;
			}
			this.selectBillBusinessFun();
		},
		onBackPress() {
			uni.$off('deleteKhFun', that.deleteKhFun)
			uni.$off('updateListByIndex', that.updateListByIndex)
			uni.$off('addItemInListFun', that.addItemInListFun)
			uni.$off('cxGetDataFun', that.cxGetDataFun)
		},
		methods: {
			selectBillBusinessFun: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetBillBusinessList',
							params: [{
								name: 'TypeStatus',
								value: this.TypeStatus
							}, {
								name: 'TypeNo',
								value: this.this
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						if (that.pageIndex == 1) {
							that.list = [];
						}
						if (res.data == 20) {
							that.pageIndex += 1;
							that.isMore = true;
						} else {
							that.isMore = false;
						}
						that.list = that.list.concat(data);
					},
				})
			},
			//点击方法
			CardClickFun: function(item) {
				uni.$emit('bjdKehuBindFun', {
					CustomerID: item.CustomerID,
					CustomerName: item.CustomerName,
					PlanDepartmentID: item.PlanDepartmentID,
					PlanDepartmentName: item.PlanDepartmentName,
					SaleUserID: item.SaleUserID,
					SaleUserName: item.SaleUserName,
					Remark: item.Remark,
					CustomerAddress: item.CustomerAddress,
					CustomerPhone: item.CustomerPhone,
					CustomerLinkName: item.CustomerLinkName,
				})
				uni.navigateBack();
			},

		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}

	.card {
		width: 698rpx;
		padding: 26rpx 26rpx 10rpx;
		margin: 32rpx 26rpx;
		box-sizing: border-box;
		border-radius: 8rpx;
		box-shadow: #d8d8d8 0px 0px 16rpx;
		position: relative;
		background-color: #FFFFFF;
	}

	.genjinBtn {
		position: absolute;
		right: 26rpx;
		top: 26rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		text-align: center;
		padding: 6rpx 16rpx;
		border-radius: 6rpx;
		font-size: 14px;
	}

	.genjinBtn:active {
		background-color: #13B8FF;
	}

	.topRow {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.txView {
		width: 128rpx;
		height: 128rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 26rpx;
	}

	.txViewImg {
		width: 100%;
		height: 100%;
	}

	.info {
		width: 492rpx;
		font-size: 15px;
		color: #000;
	}

	.name {
		font-size: 16px;
		margin-bottom: 8rpx;
		color: #000000;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: bold;
	}

	.bottomRow {
		width: 100%;
		padding-top: 16rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-top: 1rpx solid #f0f0f0;
		font-size: 15px;
		color: #007AFF;
	}

	.lxRow {
		display: flex;
		align-items: center;
	}

	.lxRow>image {
		width: 52rpx;
		height: 52rpx;
		margin-right: 26rpx;
	}

	.bqRow {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.bqRow>text {
		font-size: 14px;
		color: #888888;
	}

	.bqRow>view {
		padding: 6rpx 16rpx;
		font-size: 14px;
		background-color: rgba(255, 85, 127, 0.1);
		color: #ff5500;
		border-radius: 10rpx;
		margin: 6rpx 26rpx 20rpx 0;
	}
</style>