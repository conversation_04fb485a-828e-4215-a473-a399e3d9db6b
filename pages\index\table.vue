<template>
	<view class="c-column" style="width: 100%;height: 40vh;">
		<view class="c-row" style="margin: 5px;">
			<button type="default" @click="addRow">新增</button>
			<button type="default" @click="removeRow">删除</button>
			<button type="default" @click="saveRow">保存</button>
		</view>
		<c-table class="c-filling" ref="table" :initTable="initTable" @tableEvents="tableEvents"
			@slotEvents="slotEvents" @initComplete="initComplete" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				initTable: {
					columns: [{
							type: "checkbox",
							width: 50,
						},
						{
							type: "seq",
							width: 50,
							title: "序号",
						},
						{
							field: "Name",
							title: "名称",
							width: 150,
							editRender: {
								name: "input",
								props: {
									type: "input"
								}
							}
						},
						{
							field: "Sex",
							title: "年龄",
							width: 100,
							editRender: {
								name: "$input",
								props: {
									type: "integer"
								}
							},
						},
						{
							field: "Gender",
							title: "性别",
							width: 100,
							editRender: {
								name: "$select",
								options: [{
										label: '女',
										value: '0'
									},
									{
										label: '男',
										value: '1'
									}
								]
							}
						},
						{
							field: "birthday",
							title: "出生日期",
							width: 150,
							editRender: {
								name: "$input",
								props: {
									type: "date"
								}
							}
						},
						{
							title: '操作',
							width: 130,
							slots: {
								default: 'operate'
							},
							fixed: "right"
						}
					],
					loading: false, //加载遮罩
					//注册表格事件列点击事件，行点击事件
					initEventsList: ["header-cell-click", "cell-click"],
					//插槽内容
					initSlotString: `
					<template #operate="{ row }">
						<vxe-button @click="slotEvents(row)">获取行对象</vxe-button>
					</template>
					`
				},
				data: [{
					Name: "张三",
					Sex: 18,
					Gender: "1"
				}, {
					Name: "小红",
					Sex: 19,
					Gender: "0",
					birthday: new Date()
				}, {
					Name: "李四",
					Sex: 20,
					Gender: "1"
				}, {
					Name: "王五",
					Sex: 20,
					Gender: "1"
				}]
			}
		},
		created() {},
		methods: {
			//表格加载完成回调函数
			initComplete() {
				this.$refs.table.setTableProps({
					loading: true,
				})
				//模拟异步加载数据
				setTimeout(() => {
					this.$refs.table.setTableProps({
						loading: false,
						data: this.data,
						editRules: {
							Name: [{
								required: true,
								message: '请填写名称'
							}],
							Sex: [{
								type: "number",
								min: 0,
								max: 150,
								message: '格式不正确'
							}],
						}
					})
				}, 1000)
			},
			addRow() {
				this.$refs.table.callMethods("insert", {
					Sex: 18,
					Gender: "1",
				})
			},
			removeRow() {
				this.$refs.table.callMethods("removeCheckboxRow")
			},
			async saveRow() {
				let validateRES = await this.$refs.table.callMethods("validate")
				//判断校验是否通过
				if (validateRES != undefined) return
				uni.showToast({
					title: "校验通过"
				})
				this.$refs.table.callMethods("getRecordset").then(res => {
					console.log(res);
				})
			},
			tableEvents(p) {
				console.log(p);
			},
			slotEvents(p) {
				console.log(p);
			}
		}
	}
</script>

<style scoped>
	.c-column {
		display: flex;
		flex-direction: column;
	}

	.c-filling {
		height: 10px;
		flex-grow: 1;
	}

	.c-row {
		display: flex;
		flex-direction: row;
	}
</style>
