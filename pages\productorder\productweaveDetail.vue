<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">排产单号：{{ProductWeaveNo}}</text>
				<text class="title" style="width:200px;">排产日期：{{$u.timeFormat(ProductWeaveDate, 'yyyy-mm-dd')}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">织厂名称：{{WeaveFactoryName}}</text>
				<text class="title" style="width:200px;">商品编码：{{FabricEmbryoCodeNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">坯布名称：{{FabricEmbryoName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">坯布颜色：{{EmbryoCodeName}}</text>
				<text class="title" style="width:200px;">日产条数：{{WeaveMachineDayYieldQty}}条</text>
			</u-form-item>
			<view @click="pickerSelectFun('进度状态')" class="flex-white-plr26 ptb20 bdb_f5">
				<text>进度状态</text>
				<view class="bluecolor" :class="WeaveCompleteTypeName ? '' : 'cBlack'">
					{{WeaveCompleteTypeName ? WeaveCompleteTypeName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>

			<u-form-item>
				<text class="title" style="width:80px;">生产情况：</text>
				<u-input v-model="WeaveFactoryWeaveRemark" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">安排机台：</text>
				<u-input v-model="WeaveFactoryMachineNos" type="number" placeholder="请输入数字"
					@input="TypeInputWeaveFactoryMachineNos($event)" /></text>
				<text class="title" style="width:80px;">染纱货期：</text>
				<u-input v-model="DyeWorksDyeYarnTermDate" /></text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">派纱时间：</text>
				<u-input v-model="WeaveFactorySendYarn" />
				<text class="title" style="width:80px;">上机时间：</text>
				<u-input v-model="WeaveFactoryMachineBeginDate" /></text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:80px;">派纱备注：</text>
				<u-input v-model="WeaveFactorySendRemark" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">当前跟前天累计条数：{{TowDayInRoll}}条</text>
				<text class="title" style="width:200px;">当天收布条数：{{ToDayInRoll}}条</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">累计条数：{{SumInRoll}}条</text>
				<text class="title" style="width:80px;">未织条数：</text>
				<u-input v-model="NotProductWeaveRoll" type="number" placeholder="请输入数字"
					@input="TypeInputNotProductWeaveRoll($event)" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">欠胚数量：{{NotProductRoll}}条</text>
				<text class="title" style="width:80px;">织厂存胚：</text>
				<u-input v-model="WeaveFactoryStoreRoll" type="number" placeholder="请输入数字"
					@input="TypeInputNotWeaveFactoryStoreRoll($event)" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:100px;">回复交货时间：</text>
				<u-input v-model="WeaveFactoryReplyTermDate" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:100px;">更改货期原因：</text>
				<u-input v-model="WeaveFactoryReplyRemark" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">成品间距：{{GoodsWeaveSpaceNames}}</text>
				<text class="title" style="width:200px;">针 寸 数：{{NeedleInch}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:400px;">织厂地址：{{WeaveFactoryContactAddress}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">联 系 人：{{WeaveFactoryContactName}}</text>
				<text class="title" style="width:200px;">织厂电话：{{WeaveFactoryContactPhone}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">排产条数：{{WeaveRoll}}</text>
				<text class="title" style="width:200px;">排产重量：{{WeaveQty}}</text>
			</u-form-item>
			<!--提交按钮-->
			<view class="submitView">
				<u-button type="primary" class="submitBtn" :ripple="true" ripple-bg-color="#909399"
					@click="submitBtnFun"> {{pageType ? '保存' : '提交'}}
				</u-button>
			</view>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="YarnDetailList" />
		</view>

		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				BillMasterID: 0,
				BillDetailID: 0,
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				selectShow: false,
				selectList: [],
				selectType: '',
				pageType: '',
				ProductWeaveNo: '',
				ProductWeaveDate: '',
				WeaveFacctoryID: 0,
				WeaveFactoryName: '',
				WeaveFactoryContactAddress: '',
				WeaveFactoryContactName: '',
				WeaveFactoryContactPhone: '',
				FabricEmbryoNo: '',
				FabricEmbryoName: '',
				EmbryoCodeNo: '',
				EmbryoCodeName: '',
				FabricEmbryoCodeNo: '',
				GoodsWeaveSpaceNames: '',
				WeaveCompleteTypeID: 0,
				WeaveCompleteTypeName: '',
				NeedleInch: '',
				WeaveRoll: 0,
				WeaveQty: 0,
				WeaveMachineDayYieldQty: 0,
				WeaveFactoryMachineNos: '',
				WeaveFactoryWeaveRemark: '',
				WeaveFactorySendYarn: '',
				WeaveFactorySendRemark: '',
				WeaveFactoryMachineBeginDate: '',
				DyeWorksDyeYarnTermDate: '',
				WeaveFactoryReplyTermDate: '',
				WeaveFactoryReplyRemark: '',
				TowDayInRoll: 0,
				ToDayInRoll: 0,
				SumInRoll: 0,
				NotProductWeaveRoll: '',
				NotProductRoll: 0,
				WeaveFactoryStoreRoll: '',
				YarnDetailList: [],
				BaseDataList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '原料编号',
					key: 'YarnNo'
				}, {
					label: '原料名称',
					key: 'YarnName'
				}, {
					label: '色号',
					key: 'YarnColorNo'
				}, {
					label: '颜色',
					key: 'YarnColorName'
				}, {
					label: '原料比例(%)',
					key: 'YarnRate'
				}, {
					label: '原料损耗(%)',
					key: 'YarnLost'
				}, {
					label: '用纱量',
					key: 'YarnUseQty'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billmid) {
				that.BillMasterID = e.billmid;
				that.BillDetailID = e.billdid;
			}
			that.ProductWeaveDetail();


			setTimeout(() => {
				that.getBaseDataList();
			}, 1000);

			console.log("---BillID->" + this.BillMasterID + '--BillDetailID->' + this.BillDetailID);

		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			TypeInputNotProductWeaveRoll(e, val) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.NotProductWeaveRoll = e.replace(inputRule, '');
				})
			},

			TypeInputNotWeaveFactoryStoreRoll(e, val) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.WeaveFactoryStoreRoll = e.replace(inputRule, '');
				})
			},

			TypeInputWeaveFactoryMachineNos(e, val) {
				// 只能输入数字的验证;
				const inputType = /[^\d]/g //想限制什么类型在这里换换正则就可以了
				this.$nextTick(function() {
					this.WeaveFactoryStoreRoll = e.replace(inputRule, '');
				})
			},

			getBaseDataList() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetBaseDataList',
							params: [{
								name: 'TypeNo',
								value: 'ProductWeave.WeaveCompleteTypeName'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BaseDataList.push({
									value: aResultData[i].BaseDataID,
									label: aResultData[i].BaseDataName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.BaseDataList = [];
						} else {
							this.BaseDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			// 提交按钮方法
			submitBtnFun: function() {
				console.log("------->>>" + this.BillDetailID);
				if (this.BillDetailID == 0) {
					//this.playError();
					this.BillDataMessage = '当前单号为空，不能提交！';
					return;
				}
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_ProductWeaveTrackData',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.BillMasterID
								},
								{
									name: '@BillDetailID',
									value: this.BillDetailID
								},
								{
									name: '@WeaveCompleteTypeName',
									value: this.WeaveCompleteTypeName
								},
								{
									name: '@WeaveFactoryMachineNos',
									value: this.WeaveFactoryMachineNos
								},
								{
									name: '@WeaveFactoryWeaveRemark',
									value: this.WeaveFactoryWeaveRemark
								},
								{
									name: '@WeaveFactorySendYarn',
									value: this.WeaveFactorySendYarn
								}, {
									name: '@WeaveFactorySendRemark',
									value: this.WeaveFactorySendRemark
								},
								{
									name: '@WeaveFactoryMachineBeginDate',
									value: this.WeaveFactoryMachineBeginDate
								},
								{
									name: '@DyeWorksDyeYarnTermDate',
									value: this.DyeWorksDyeYarnTermDate
								},
								{
									name: '@WeaveFactoryReplyTermDate',
									value: this.WeaveFactoryReplyTermDate
								}, {
									name: '@WeaveFactoryReplyRemark',
									value: this.WeaveFactoryReplyRemark
								},
								{
									name: '@NotProductWeaveRoll',
									value: this.NotProductWeaveRoll
								},
								{
									name: '@WeaveFactoryStoreRoll',
									value: this.WeaveFactoryStoreRoll
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								uni.showToast({
									icon: 'none',
									title: '提交成功！'
								});

								uni.navigateBack({
									delta: 1
								})
							} else {
								uni.showToast({
									icon: 'none',
									title: '提交出错！' + aResultData.BillDataMessage
								});
								return;
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '提交出错！'
							});
							return;
						}
					},
					fail: (error) => {
						//this.playError();
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！'
						});
					},
				})
			},

			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				that.selectList = [];
				if (str == '进度状态') {
					console.log("--<<<<<->>>" + str);
					that.selectList = this.BaseDataList;
				}

				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '进度状态') {
					that.WeaveCompleteTypeID = e[0].value;
					that.WeaveCompleteTypeName = e[0].label;
				}
			},

			ProductWeaveDetail: function() {
				if (this.BillMasterID == 0) {
					this.BillMasterID = 30685
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetProductWeaveDetailSQL',
							params: [{
								name: 'BillID',
								value: this.BillMasterID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.YarnDetailList = res.data.data;
							this.ProductWeaveNo = aResultData[0].ProductWeaveNo;
							this.ProductWeaveDate = aResultData[0].ProductWeaveDate;
							this.WeaveFactoryName = aResultData[0].WeaveFactoryName;
							this.WeaveCompleteTypeName = aResultData[0].WeaveCompleteTypeName;
							this.WeaveFactoryContactAddress = aResultData[0].WeaveFactoryContactAddress;
							this.WeaveFactoryContactName = aResultData[0].WeaveFactoryContactName;
							this.WeaveFactoryContactPhone = aResultData[0].WeaveFactoryContactPhone;
							this.FabricEmbryoCodeNo = aResultData[0].FabricEmbryoCodeNo;
							this.FabricEmbryoNo = aResultData[0].FabricEmbryoNo;
							this.FabricEmbryoName = aResultData[0].FabricEmbryoName;
							this.EmbryoCodeNo = aResultData[0].EmbryoCodeNo;
							this.EmbryoCodeName = aResultData[0].EmbryoCodeName;
							this.GoodsWeaveSpaceNames = aResultData[0].GoodsWeaveSpaceNames;
							this.NeedleInch = aResultData[0].NeedleInch;
							this.WeaveMachineDayYieldQty = aResultData[0].WeaveMachineDayYieldQty;
							this.WeaveFactoryMachineNos = aResultData[0].WeaveFactoryMachineNos;
							this.WeaveFactoryWeaveRemark = aResultData[0].WeaveFactoryWeaveRemark;
							this.WeaveFactorySendYarn = aResultData[0].WeaveFactorySendYarn;
							this.WeaveFactoryMachineBeginDate = aResultData[0]
							.WeaveFactoryMachineBeginDate;
							this.WeaveFactorySendRemark = aResultData[0].WeaveFactorySendRemark;
							this.WeaveFactoryMachineBeginDate = aResultData[0]
							.WeaveFactoryMachineBeginDate;
							this.DyeWorksDyeYarnTermDate = aResultData[0].DyeWorksDyeYarnTermDate;
							this.WeaveFactoryReplyTermDate = aResultData[0].WeaveFactoryReplyTermDate;
							this.WeaveFactoryReplyRemark = aResultData[0].WeaveFactoryReplyRemark;
							this.TowDayInRoll = aResultData[0].TowDayInRoll;
							this.ToDayInRoll = aResultData[0].ToDayInRoll;
							this.SumInRoll = aResultData[0].SumInRoll;
							this.NotProductWeaveRoll = aResultData[0].NotProductWeaveRoll;
							this.NotProductRoll = aResultData[0].NotProductRoll;
							this.WeaveFactoryStoreRoll = aResultData[0].WeaveFactoryStoreRoll;
							this.WeaveRoll = aResultData[0].WeaveRoll.toFixed(2) + '条';
							this.WeaveQty = aResultData[0].WeaveQty.toFixed(2) + 'Kg';
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.YarnDetailList = [];
						} else {
							this.YarnDetailList = [];
						}

					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
