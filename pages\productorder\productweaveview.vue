<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">排产单号：{{item.ProductWeaveNo}}  {{item.WeaveFactoryName}}</view>
		<view class="cardRow">
			<view>排产日期：</view>
			<view>{{item.ProductWeaveDate}}</view>
		</view>
		<view class="cardRow">
			<view>商品编码：</view>
			<view>{{item.FabricEmbryoNo}}-{{item.EmbryoCodeNo}}#</view>
		</view>
		<view class="cardRow">
			<view>商品名称：</view>
			<view>{{item.FabricEmbryoName}}</view>
		</view>
		<view class="cardRow">
			<view>商品颜色：</view>
			<view>{{item.EmbryoCodeName}}</view>
		</view>
		<view class="cardRow">
			<text class="fzrLeft">进度状态：</text>
			<text class="fzrRight">{{item.WeaveCompleteTypeName}}</text>
		</view>
		<view class="cardRow">
			<view class="fzrLeft">生产情况：</view>
			<view class="fzrRight">{{item.WeaveFactoryWeaveRemark}}</view>
		</view>
		<view class="cardRow">
			<view>排产数量：</view>
			<text class="fzrRight">{{item.WeaveRoll}}条   {{item.WeaveQty}}Kg</text>
		</view>
		<view class="cardRow">
			<view>已产数量：</view>
			<text class="fzrRight">{{item.HasProductRoll}}条   {{item.HasProductQty}}Kg</text>
		</view>
		<view class="cardRow">
			<view>派纱时间：</view>
			<view>{{item.WeaveFactorySendYarn}}</view>
		</view>
		<view class="cardRow">
			<view>派纱备注：</view>
			<view>{{item.WeaveFactorySendRemark}}</view>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<u-icon name="arrow-right" size="36"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				//uni.$bjInfo = this.item;
				console.log("----->>>>" + this.index);
				uni.navigateTo({
					url: '/pages/saleship/detail?index=' + this.index
				})
			}
		}
	}
</script>

<style>
	
</style>

