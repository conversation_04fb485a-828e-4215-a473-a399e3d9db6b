<template>
  <view>
    <button type="primary" @click="onShowDatePicker('range')">单据日期  {{range[0]}} - {{range[1]}}</button>
    
    <view class="u-demo-block">
      <view class="u-page__tag-item">
        <u-search v-model="searchBillNo" :show-action="false" 
          @custom="onTabChange" @search="onTabChange" 
          placeholder="请输入单号或往来单位">
        </u-search>
      </view>
    </view>
    <u-sticky>
        <div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
            <u-tabs :list="tabList" name="title" active-color="red" :is-scroll="false" 
        :current="activeTabIndex" @change="onTabChange"></u-tabs>
        </div>
    </u-sticky>
    <dataNull v-if="ItemDataList.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关出仓资料" title1="请添加或者更换搜索添加">
    </dataNull>
    <scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" 
      refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray">
      <view v-for="(item, index) in ItemDataList" :key="index" @click="cardClickFun(item, index)">
        <StoreFabricBusinessOutItem :item="item" :isSelect="isSelect" :index="index"></StoreFabricBusinessOutItem>
      </view>
    </scroll-view>
    <addBtn url="./storefabricBusinessOutAdd"></addBtn>
    <mx-date-picker :show="showPicker" :type="BillDatetype" :value="BillDatevalue"
      :show-tips="true" :begin-text="'开始'" :end-text="'结束'" 
      :show-seconds="false" @confirm="BillDateConfirm" @cancel="BillDateConfirm" />
  </view>
</template>

<script>
  let that = '';
  import addBtn from '@/components/addBtn/addBtn.vue'
  import StoreFabricBusinessOutItem from '@/components/card/storefabricBusinessOutItem.vue';
  import dataNull from '@/components/dataNull/dataNull.vue';
  import util from '../../common/util';
  import MxDatePicker from "@/components/mx-datepicker/mx-datepicker.vue";
  import { processDataOut } from '@/common/handBinary/index.js';
  export default {
    components: {
      addBtn,
      StoreFabricBusinessOutItem,
      dataNull,
      MxDatePicker
    },
    data() {
      return {
        tabList: [
            {title: '未审核', status: '0'},
            {title: '已审核', status: '1'},
        ],
        activeTabIndex: 0,
        ItemDataList: [],
        pageIndex: 1,
        isMore: true,
        scrollHeight: '667px',
        triggered: false,
        isSelect: false,
        searchBillNo: '',
        showPicker: false,
        BillBeginDate: new Date(new Date()-24*60*60*1000).toLocaleDateString(),
        BillEndDate: new Date().toLocaleDateString(),
        range: [new Date(new Date()-24*60*60*1000).toLocaleDateString(), new Date().toLocaleDateString()],
        BillDatetype: 'range',
        BillDatevalue: ''
      }
    },
    onLoad(e) {
      that = this;
      uni.getSystemInfo({
        success(res) {
          that.scrollHeight = res.windowHeight - 40 + 'px';
        }
      })
      that.StoreFabricBusinessOutData();
    },
    watch: {
        activeTabIndex: {
            deep: true, // 深度监听
            handler(newVal, oldVal) {
              console.log("activeTabIndex参数改变，即将刷新...", `新值：${newVal}`, `旧值：${oldVal}`);
              this.onTabChange(newVal);
            }
        }
    }, 
    // 上拉加载
    onReachBottom: function () {
      console.log("activeTabIndex参数改变，即将刷新..." + this.activeTabIndex);
    },
     onBackPress() {
      /*uni.$off('deleteCardFun', that.deleteCardFun);
      uni.$off('updateBjdListByIndex', that.updateBjdListByIndex)
      uni.$off('addBjdItemInListFun', that.addBjdItemInListFun);*/
    }, 
    methods: {
      onTabChange(index) {
        const _self = this;
        this.activeTabIndex = index;
        if (this.activeTabIndex == 0) {
          console.log("---->>未审核<<----");
          this.StoreFabricBusinessOutData();
        } else {
          console.log("---->>已审核<<----");
          this.StoreFabricBusinessOutDataCommit();
        }
      },

      onShowDatePicker(type){//显示
        this.BillDatetype = type;
        this.showPicker = true;
        this.BillDatevalue = this[type];
      },
      
      BillDateConfirm(e) {//选择
        this.showPicker = false;
        if(e) {
          this[this.BillDatetype] = e.value; 
          this.BillBeginDate = e.value[0];
          this.BillEndDate = e.value[1];
        }
      }, 
      
      StoreFabricBusinessOutData: function() {
        let isLoadMore = false
        that.ItemDataList = [];
        let aCommitStatus = 0;
        if (this.activeTabIndex  == 0) {
          aCommitStatus = 0
        }
        else if (this.activeTabIndex  == 1) {
          aCommitStatus = 1
        };
        this.$u.api.getGfmOtherDeliveryOrderList({
          order_no: this.searchBillNo,
          // src_order_no: this.searchBillNo,
          // business_status_ids: this.tabList[this.activeTabIndex].status, // 根据tab传递对应的状态组合
          page: this.pageIndex,
          size: this.pageSize,
        }).then(res => {
          console.log("API 响应数据:", res);
          this.triggered = false;
          // 根据 HTTP 拦截器的处理，res 已经是处理后的数据对象
          // 如果 res 是数组，直接使用；如果是对象且有 list 属性，使用 list
          // let data = Array.isArray(res) ? res : (res.list || res.data || []);
          const list = processDataOut(res.list);
          console.log("处理后的数据:", list);
          that.ItemDataList = list;
        }).catch(e => {
          console.error("API 请求失败:", e);
          uni.showToast({
            title: e.msg || '请求失败',
            icon: "none",
          });
        }).finally(() => {
          this.loading = false;
          uni.hideLoading();
          if (!isLoadMore) {
            setTimeout(() => {
              this.triggered = false;
              this.isRefreshing = false;
            }, 500);
          }
        })
        // uni.request({
        //   url: util.apiurl + 'rest/db/opensql',
        //   data: {
        //     token: getApp().globalData.Token,
        //     format: 'json',
        //     data: {
        //       db_name: getApp().globalData.AppDBName,
        //       sql_command_id: 'APP.GetStoreFabricBusinessOutDataSQL',
        //       params: [{
        //         name: 'CommitStatus',
        //         value: aCommitStatus
        //       }, {
        //         name: 'BillNo',
        //         value: '%' + this.searchBillNo + '%'
        //       }, {
        //         name: 'LOGINID',
        //         value: getApp().globalData.LoginID
        //       }, {
        //         name: 'PLANLOGINID',
        //         value: getApp().globalData.LoginID
        //       }]
        //     },
        //   },
        //   success: (res) => {
        //     this.triggered = false;
        //     let data = res.data.data;
        //     console.log("---->" + JSON.stringify(res.data));
        //     that.ItemDataList = [];
        //     that.ItemDataList = that.ItemDataList.concat(data);
        //   },
        // })
      },
      
      StoreFabricBusinessOutDataCommit: function() {
        that.ItemDataList = [];
        let aCommitStatus = 0;
        if (this.activeTabIndex  == 0) {
          aCommitStatus = 0
        }
        else if (this.activeTabIndex  == 1) {
          aCommitStatus = 1
        };
      
        uni.request({
          url: util.apiurl + 'rest/db/opensql',
          data: {
            token: getApp().globalData.Token,
            format: 'json',
            data: {
              db_name: getApp().globalData.AppDBName,
              sql_command_id: 'APP.GetStoreFabricBusinessOutDataCommitSQL',
              params: [{
                name: 'CommitStatus',
                value: '1'
              },{
                name: 'BeginDate',
                value: this.BillBeginDate
              },{
                name: 'EndDate',
                value: this.BillEndDate
              },{
                name: 'BillNo',
                value: '%'+this.searchBillNo+'%'
              },{
                name: 'LOGINID',
                value: getApp().globalData.LoginID
              },{
                name: 'PLANLOGINID',
                value: getApp().globalData.LoginID
              }]
            },
          },
          success: (res) => {
            this.triggered = false;
            let data = res.data.data;
            that.ItemDataList = [];
            console.log("---->" + JSON.stringify(res.data));
            that.ItemDataList = that.ItemDataList.concat(data);
          },
        })
      },
      
      // 卡片点击方法
      cardClickFun: function(item, index) {
        uni.navigateTo({
          url: '/pages/storefabric/storefabricBusinessOutDetail?billid=' + item.BillMasterID
        })
      },
    }
  }
</script>

<style>
  page {
    background-color: #F8F8F8;
  }
  button{
      margin: 20upx;
      font-size: 28upx;
  }
</style>
