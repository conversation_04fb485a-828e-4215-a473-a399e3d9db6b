<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="80" label="单号:">
				<text class="title" style="width:300px;">{{storeInData.BillGUID}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="仓位:">
				<input type="text" v-model="storeInData.StoreStationNo" :focus="aPositionFocus0" style="width:80px;"
					@confirm="GetStoreStationName" />
				<text class="title" style="width:100px;">名称:  {{storeInData.StoreStationName}}</text>
				<button class="mini-btn" type="primary" size="mini" @tap="CreateNewBillMaster">新增单据</button>
			</u-form-item>
			<u-form-item label-width="80" label="机号:">
				<input type="text" v-model="storeInData.WeaveMachineNo" :focus="aPositionFocus1" style="width:150px;"
					@confirm="GetWeaveMachineNo"/>
				<text class="title" style="width:50px;">重量:  </text>
				<input type="text" v-model="storeInData.Qty" :focus="aPositionFocus2" style="width:150px;"
				@confirm="GetStoreInDataQty"/>
			</u-form-item>
			<u-form-item label-width="80" label="条码:">
				<input type="text" v-model="storeInData.QRBarCode" :focus="aPositionFocus3" style="width:150px;"
					@confirm="StoreFabricBarCodeScanIn" />
				<text class="title" style="width:150px;">布号:  {{storeInData.FabricBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="织厂:">
				<text class="title" style="width:150px;">{{storeInData.WeaveFactoryName}}</text>
				<text class="title" style="width:150px;">编号:  {{storeInData.FabricEmbryoNo}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="品名:">
				<text class="title" style="width:150px;">{{storeInData.FabricEmbryoName}}</text>
				<text class="title" style="width:300px;">颜色: {{storeInData.EmbryoCodeNo}}{{storeInData.EmbryoCodeName}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="条数:">
				<text class="title" style="width:150px;">{{storeInData.SumRoll}}</text>
				<text class="title" style="width:150px;">重量:{{storeInData.SumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="storeInData.headersMaster"
			:contents="storeInData.saleInDetailDataList" />
		</view>
	</view>
</template>
<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio,
		guid
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				aPositionFocus0: true,
				aPositionFocus1: false,
				aPositionFocus2: false,
				aPositionFocus3: false,
				storeInData: {
					BillGUID:'',
					QRBarCode: '',
					StoreNameID: '0',
					StoreStationNo: '',
					StoreStationName: '',
					WeaveFactoryID: 0,
					WeaveFactoryName: '',
					FabricEmbryoID: 0,
					FabricEmbryoNo: '',
					FabricEmbryoName: '',
					EmbryoCodeID: 0,
					EmbryoCodeNo: '',
					EmbryoCodeName: '',
					WeaveMachineNo: '',
					FabricBillNo: '',
					Qty: '',
					SumRoll: '',
					SumQty: '',
					saleInDetailDataList: [],
					headersMaster: [{
						label: '织厂',
						key: 'WeaveFactoryName'
					},{
						label: '编号',
						key: 'FabricEmbryoNo'
					}, {
						label: '色号',
						key: 'EmbryoCodeNo'
					}, {
						label: '条数',
						key: 'Roll'
					}, {
						label: '重量',
						key: 'Qty'
					}, {
						label: '颜色',
						key: 'EmbryoCodeName'
					}, {
						label: '机号',
						key: 'WeaveMachineNo'
					}],
				},
				BillDataMessage: '',
			};
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},
			GetStoreStationName: function() {
				if (this.storeInData.BillGUID == ''){
					this.playError();
					this.BillDataMessage = '请先按新增单据再输入扫描！';
					return;
				};
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.storeInData.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.storeInData.StoreStationName = aResultData[i].StoreStationName;
								this.storeInData.StoreNameID = parseInt(aResultData[i].StoreNameID);
							};
							this.aPositionFocus0 = false;
							this.$nextTick(() => {
								this.aPositionFocus1 = true;
								this.aPositionFocus2 = false;
								this.aPositionFocus3 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
								this.storeInData.StoreNameID = 0,
								this.storeInData.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},
			GetWeaveMachineNo(){
				if (this.storeInData.BillGUID == ''){
					this.playError();
					this.BillDataMessage = '请先按新增单据再输入扫描！';
					return;
				};
				this.aPositionFocus1 = false;
				this.$nextTick(() => {
					this.aPositionFocus0 = false;
					this.aPositionFocus2 = true;
					this.aPositionFocus3 = false;
				});
				this.playSuccess();
			},
			GetStoreInDataQty(){
				if (this.storeInData.BillGUID == ''){
					this.playError();
					this.BillDataMessage = '请先按新增单据再输入扫描！';
					return;
				};
				this.aPositionFocus2 = false;
				this.$nextTick(() => {
					this.aPositionFocus0 = false;
					this.aPositionFocus1 = false;
					this.aPositionFocus3 = true;
				});
				this.playSuccess();
			},

			CreateNewBillMaster() {
				this.storeInData.BillGUID = guid();
				console.log("-------->" + this.storeInData.BillGUID + "<----");
				this.aPositionFocus0 = false;
				this.$nextTick(() => {
					this.aPositionFocus0 = true;
					this.aPositionFocus1 = false;
					this.aPositionFocus2 = false;
					this.aPositionFocus3 = false;
				});
				this.playSuccess();
			},

			GetStoreInDataList: function() {
				this.CarryDetailList = [];
				this.BillID = 0;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreFabricInStoreMaster',
							params: [{
								name: 'GUID',
								value: this.storeInData.BillGUID
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							this.storeInData.saleInDetailDataList = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.storeInData.saleInDetailDataList.push(aResultData[i]);
							};
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus2 = true;
							});

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.playError();
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus2 = true;
							});
						} else {
							this.playError();
							this.testFocus2 = false;
							this.$nextTick(() => {
								this.testFocus2 = true;
							});
						}
					},
					fail: (error) => {
						this.playError();
						this.testFocus2 = false;
						this.$nextTick(() => {
							this.testFocus2 = true;
						});
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			StoreFabricBarCodeScanIn() {
				if (this.storeInData.BillGUID == ''){
					this.playError();
					this.BillDataMessage = '请先按新增单据再输入扫描！';
					return;
				};
				if (this.storeInData.WeaveMachineNo == ''){
					this.playError();
					this.BillDataMessage = '请先输入机号再扫描！';
					return;
				};
				if (this.storeInData.Qty == ''){
					this.playError();
					this.BillDataMessage = '请先输入重量再扫描！';
					return;
				};
				var aStrBarCode = this.storeInData.QRBarCode;
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				console.log("-------->" + aStrBarCode + "<----");
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreFabricBarCodeScanIn',
							method: 'open_proc',
							params: [{
									name: '@BarCode',
									value: aStrBarCode
								},{
									name: '@SaleOrderMasterID',
									value: '0'
								},{
									name: '@CustomerID',
									value: '0'
								},{
									name: '@FabricGoodsID',
									value: '0'
								},{
									name: '@GoodsCodeID',
									value: '0'
								},{
									name: '@WeaveFactoryID',
									value: '0'
								},{
									name: '@WeaveMachineNo',
									value: ''
								},{
									name: '@ImportFileName',
									value: this.storeInData.BillGUID
								},{
									name: '@DeleteStatus',
									value: '0'
								},{
									name: '@StoreStationName',
									value: this.storeInData.StoreStationName
								},{
									name: '@InWeaveMachineNo',
									value: this.storeInData.WeaveMachineNo
								},{
									name: '@InQty',
									value: this.storeInData.Qty
								},{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.storeInData.WeaveFactoryID = aResultData.WeaveFactoryID;
								this.storeInData.WeaveFactoryName = aResultData.WeaveFactoryName;
								this.storeInData.FabricEmbryoID = aResultData.FabricEmbryoID;
								this.storeInData.FabricEmbryoNo = aResultData.FabricEmbryoNo;
								this.storeInData.FabricEmbryoName = aResultData.FabricEmbryoName;
								this.storeInData.EmbryoCodeID = aResultData.EmbryoCodeID;
								this.storeInData.EmbryoCodeNo = aResultData.EmbryoCodeNo + '#';
								this.storeInData.EmbryoCodeName = aResultData.EmbryoCodeName;
								this.storeInData.FabricBillNo = aResultData.FabricBillNo;
								this.storeInData.SumRoll = aResultData.SumRoll;
								this.storeInData.SumQty = aResultData.SumQty;
								this.BillDataMessage = '扫描成功！';
								this.storeInData.Qty = '';
								this.storeInData.QRBarCode = '';
								this.GetStoreInDataList();
								this.aPositionFocus3 = false;
								this.$nextTick(() => {
									this.aPositionFocus0 = false;
									this.aPositionFocus1 = false;
									this.aPositionFocus2 = true;
								});
							} else {
								this.playError();
								this.storeInData.Qty = '';
								this.storeInData.QRBarCode = '';
								this.BillDataMessage = '入仓出错，' + aResultData.BillDataMessage;
								this.aPositionFocus3 = false;
								this.$nextTick(() => {
									this.aPositionFocus0 = false;
									this.aPositionFocus1 = false;
									this.aPositionFocus2 = true;
								});
								return;
							}
						} else {
							this.playError();
							this.storeInData.Qty = '';
							this.storeInData.QRBarCode = '';
							this.BillDataMessage = '入仓出错，' + res.data.msg;
							this.aPositionFocus3 = false;
							this.$nextTick(() => {
								this.aPositionFocus0 = false;
								this.aPositionFocus1 = false;
								this.aPositionFocus2 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.aPositionFocus3 = false;
						this.$nextTick(() => {
							this.aPositionFocus0 = false;
							this.aPositionFocus1 = false;
							this.aPositionFocus2 = true;
						});
						this.BillDataMessage = '入仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			}
		},
	}
</script>
<style lang="scss" scoped>
	@import 'index.scss';

	page {
		background-color: #f5f5f5;
	}

	.wrap .search {
		background: #ffffff;
	}

	.apply-text {
		font-size: 28rpx;
		color: #333333;

	span {
			color: #999999;
		}
	}

	.user-images {
		width: 28px;
		height: 28px;
		margin-right: 8px;
	}

	.apply-list-foot {
		font-size: 28rpx;
	}

	.personnel-list {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.personnel-user {
			position: relative;
			margin: 5px 9px 0;
		}

		.user-images {
			width: 48px;
			height: 48px;
			margin-right: 0;

		}

		.iconfont {
			position: absolute;
			top: -12px;
			right: -5px;
			color: #FE0100;
		}
	}
</style>
