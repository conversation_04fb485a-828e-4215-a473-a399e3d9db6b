<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="80" label="单号:">
				<text class="title" style="width:300px;">{{storeOutData.BillGUID}}</text>
			</u-form-item>
			<u-form-item  label="染厂" label-width="150" >
				<u-input type="select" :select-open="DyeWorksNameShow"
					v-model="storeOutData.DyeWorksName" placeholder="请选择染厂名称"
					@click="DyeWorksNameShow = true">
				</u-input>
			</u-form-item>
			<u-form-item label-width="80" label="条码:">
				<input type="text" v-model="storeOutData.QRBarCode" :focus="aPositionFocus0" style="width:150px;"
					@confirm="StoreFabricBarCodeScanOut" />
				<button class="mini-btn" type="primary" size="mini" @tap="CreateNewBillMaster">新增单据</button>
			</u-form-item>
			<u-form-item label-width="80" label="编号:">
				<text class="title" style="width:100px;">{{storeOutData.FabricEmbryoNo}}</text>
				<text class="title" style="width:250px;">品名: {{storeOutData.FabricEmbryoName}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="色号:">
				<text class="title" style="width:100px;">{{storeOutData.EmbryoCodeNo}}</text>
				<text class="title" style="width:250px;">颜色: {{storeOutData.EmbryoCodeName}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="织厂:">
				<text class="title" style="width:100px;">{{storeOutData.WeaveFactoryName}}</text>
				<text class="title" style="width:250px;">机号: {{storeOutData.WeaveMachineNo}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="布号:">
				<text class="title" style="width:100px;">{{storeOutData.FabricBillNo}}</text>
				<text class="title" style="width:250px;">条重: {{storeOutData.Qty}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item label-width="80" label="条数:">
				<text class="title" style="width:150px;">{{storeOutData.SumRoll}}</text>
				<text class="title" style="width:150px;">重量:{{storeOutData.SumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="storeOutData.headersMaster"
			:contents="storeOutData.StoreOutDetailDataList"/>
		</view>
		<u-action-sheet :list="DyeWorksNameList" v-model="DyeWorksNameShow"
			@click="DyeWroksNameDataCallback"></u-action-sheet>
	</view>
</template>
<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio,
		guid
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				aPositionFocus0: true,
				labelPosition: 'left',
				DyeWorksNameShow: false,
				DyeWorksNameList:[],
				storeOutData: {
					BillGUID:'',
					QRBarCode: '',
					StoreNameID: '0',
					StoreStationNo: '',
					StoreStationName: '',
					DyeWorksID:'',
					DyeWorksName: '',
					WeaveFactoryID: 0,
					WeaveFactoryName: '',
					FabricEmbryoID: 0,
					FabricEmbryoNo: '',
					FabricEmbryoName: '',
					EmbryoCodeID: 0,
					EmbryoCodeNo: '',
					EmbryoCodeName: '',
					WeaveMachineNo: '',
					FabricBillNo: '',
					Qty: '',
					SumRoll: '',
					SumQty: '',
					StoreOutDetailDataList: [],
					headersMaster: [{
						label: '编号',
						key: 'FabricEmbryoNo'
					}, {
						label: '色号',
						key: 'EmbryoCodeNo'
					}, {
						label: '条数',
						key: 'Roll'
					}, {
						label: '重量',
						key: 'Qty'
					}, {
						label: '颜色',
						key: 'EmbryoCodeName'
					}, {
						label: '机号',
						key: 'WeaveMachineNo'
					}, {
						label: '织厂',
						key: 'WeaveFactoryName'
					}],
				},
				BillDataMessage: '',
			};
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},
			DyeWroksNameDataCallback(index) {
				uni.hideKeyboard();
				this.storeOutData.DyeWorksName = this.DyeWorksNameList[index].text;
				this.storeOutData.DyeWorksID = this.DyeWorksNameList[index].CustomerID;
				console.log("------------->>>" + this.storeOutData.DyeWorksID )
				this.aPositionFocus0 = false;
				this.$nextTick(() => {
					this.aPositionFocus0 = true;
				});
			},

			CreateNewBillMaster() {
				this.storeOutData.BillGUID = guid();
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetBaseDataDyeWorksNameList',
							params: []
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							this.DyeWorksNameList = [];
							console.log("------------>>>>" + aResultData.length);
							for (var i = 0; i < aResultData.length; i++) {
								this.DyeWorksNameList.push(aResultData[i]);
							};
						}
					},
				})

				this.playSuccess();
			},

			GetStoreOutDataList: function() {
				this.BillID = 0;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreFabricOutStoreMaster',
							params: [{
								name: 'GUID',
								value: this.storeOutData.BillGUID
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							this.storeOutData.StoreOutDetailDataList = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.storeOutData.StoreOutDetailDataList.push(aResultData[i]);
							};
						}
					},
					fail: (error) => {
						this.playError();
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			StoreFabricBarCodeScanOut() {
				if (this.storeOutData.BillGUID == ''){
					this.playError();
					this.BillDataMessage = '请先按新增单据再扫描条码！';
					return;
				};
				if (this.storeOutData.DyeWorksName == ''){
					this.playError();
					this.BillDataMessage = '请先选择染厂名称再扫描条码！';
					return;
				};
				var aStrBarCode = this.storeOutData.QRBarCode;
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				console.log("-------->" + aStrBarCode + "<----");
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreFabricBarCodeScanOut',
							method: 'open_proc',
							params: [{
									name: '@BarCode',
									value: aStrBarCode
								},{
									name: '@SaleOrderMasterID',
									value: '0'
								},{
									name: '@CustomerID',
									value: '0'
								},{
									name: '@FabricGoodsID',
									value: '0'
								},{
									name: '@GoodsCodeID',
									value: '0'
								},{
									name: '@WeaveFactoryID',
									value: '0'
								},{
									name: '@WeaveMachineNo',
									value: ''
								},{
									name: '@ImportFileName',
									value: this.storeOutData.BillGUID
								},{
									name: '@DeleteStatus',
									value: '0'
								},{
									name: '@DyeWorksID',
									value: this.storeOutData.DyeWorksID
								},{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.storeOutData.WeaveFactoryID = aResultData.WeaveFactoryID;
								this.storeOutData.WeaveFactoryName = aResultData.WeaveFactoryName;
								this.storeOutData.FabricEmbryoID = aResultData.FabricEmbryoID;
								this.storeOutData.FabricEmbryoNo = aResultData.FabricEmbryoNo;
								this.storeOutData.FabricEmbryoName = aResultData.FabricEmbryoName;
								this.storeOutData.EmbryoCodeID = aResultData.EmbryoCodeID;
								this.storeOutData.EmbryoCodeNo = aResultData.EmbryoCodeNo;
								this.storeOutData.EmbryoCodeName = aResultData.EmbryoCodeName;
								this.storeOutData.WeaveMachineNo = aResultData.WeaveMachineNo;
								this.storeOutData.FabricBillNo = aResultData.FabricBillNo;
								this.storeOutData.Qty = aResultData.Qty;
								this.storeOutData.SumRoll = aResultData.SumRoll;
								this.storeOutData.SumQty = aResultData.SumQty;
								this.BillDataMessage = '扫描成功！';
								this.storeOutData.QRBarCode = '';
								this.GetStoreOutDataList();
								this.aPositionFocus0 = false;
								this.$nextTick(() => {
									this.aPositionFocus0 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓出错，' + aResultData.BillDataMessage;
								this.storeOutData.QRBarCode = '';
								this.aPositionFocus0 = false;
								this.$nextTick(() => {
									this.aPositionFocus0 = true;
								});
								return;
							}
						} else {
							this.playError();
							this.storeOutData.QRBarCode = '';
							this.BillDataMessage = '出仓出错，' + res.data.msg;
							this.aPositionFocus0 = false;
							this.$nextTick(() => {
								this.aPositionFocus0 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.aPositionFocus0 = false;
						this.$nextTick(() => {
							this.aPositionFocus0 = true;
						});
						this.BillDataMessage = '出仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			}
		},
	}
</script>
<style lang="scss" scoped>
	@import 'index.scss';

	page {
		background-color: #f5f5f5;
	}

	.wrap .search {
		background: #ffffff;
	}

	.apply-text {
		font-size: 28rpx;
		color: #333333;

	span {
			color: #999999;
		}
	}

	.user-images {
		width: 28px;
		height: 28px;
		margin-right: 8px;
	}

	.apply-list-foot {
		font-size: 28rpx;
	}

	.personnel-list {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.personnel-user {
			position: relative;
			margin: 5px 9px 0;
		}

		.user-images {
			width: 48px;
			height: 48px;
			margin-right: 0;

		}

		.iconfont {
			position: absolute;
			top: -12px;
			right: -5px;
			color: #FE0100;
		}
	}
</style>
