<template>
	<view>
		<button type="primary" @click="onShowDatePicker('range')">单据日期  {{range[0]}} - {{range[1]}}</button>
		
		<view class="u-demo-block">
			<view class="u-page__tag-item">
				<u-search v-model="searchBillNo" :show-action="false" 
					@custom="onTabChange" @search="onTabChange" 
					placeholder="请输入单号或往来单位">
				</u-search>
			</view>
		</view>
		
		<u-sticky>
		    <div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
		        <u-tabs :list="tabList" name="title" active-color="red" :is-scroll="false" 
				:current="activeTabIndex" @change="onTabChange"></u-tabs>
		    </div>
		</u-sticky>
		
		<dataNull v-if="ItemDataList.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关出仓资料" title1="请添加或者更换搜索添加">
		</dataNull>
		
		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" 
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray">
			<view v-for="(item, index) in ItemDataList" :key="index" @click="cardClickFun(item, index)">
				<GoodsBusinessInItem :item="item" :isSelect="isSelect" :index="index"></GoodsBusinessInItem>
			</view>
		</scroll-view>
		
		<addBtn url="./storeGoodsBusinessInAdd"></addBtn>
		<mx-date-picker :show="showPicker" :type="BillDatetype" :value="BillDatevalue" 
			:show-tips="true" :begin-text="'开始'" :end-text="'结束'" 
			:show-seconds="false" @confirm="BillDateConfirm" @cancel="BillDateConfirm" />
	</view>
</template>

<script>
	let that = '';
	import addBtn from '@/components/addBtn/addBtn.vue'
	import GoodsBusinessInItem from '@/components/card/storeGoodsBusinessInItem.vue';
	import util from '../../common/util';
	import MxDatePicker from "@/components/mx-datepicker/mx-datepicker.vue";
	export default {
		components: {
			GoodsBusinessInItem,
			MxDatePicker
		},
		data() {
			return {
				tabList: [
				    {title: '未审核', status: '0'},
				    {title: '已审核', status: '1'},
				],
				activeTabIndex: 0,
				ItemDataList: [],
				pageIndex: 1,
				isMore: true,
				scrollHeight: '667px',
				triggered: false,
				isSelect: false,
				searchBillNo: '',
				showPicker: false,
				BillBeginDate: new Date(new Date()-24*60*60*1000).toLocaleDateString(),
				BillEndDate: new Date().toLocaleDateString(),
				range: [new Date(new Date()-24*60*60*1000).toLocaleDateString(), new Date().toLocaleDateString()],
				BillDatetype: 'range',
				BillDatevalue: ''
			}
		},
		onLoad(e) {
			that = this;
			uni.getSystemInfo({
				success(res) {
					that.scrollHeight = res.windowHeight - 40 + 'px';
				}
			})
			that.StoreGoodsBusinessInData();
		},
		watch: {
		    activeTabIndex: {
		        deep: true, // 深度监听
		        handler(newVal, oldVal) {
					console.log("activeTabIndex参数改变，即将刷新...", `新值：${newVal}`, `旧值：${oldVal}`);
					//this.doRefresh(this.tbOrderCouponList)
		        }
		    }
		}, 
		// 上拉加载
		onReachBottom: function () {
			 console.log("activeTabIndex参数改变，111即将刷新..." + this.activeTabIndex);
			/*
		    if (this.pagination.hasNextPage) {
		        this.doInfinite()
		    }
			*/
		},
		
		 onBackPress() {
			//uni.$off('deleteCardFun', that.deleteCardFun);
			//uni.$off('updateBjdListByIndex', that.updateBjdListByIndex)
			//uni.$off('addBjdItemInListFun', that.addBjdItemInListFun);
		}, 
		methods: {
			onTabChange(index) {
			    const _self = this;
			    this.activeTabIndex = index;
				console.log("-----index------>>>>>>>" + this.activeTabIndex);
				console.log("----------->>>>>>>" + this.searchBillNo);
				if (this.activeTabIndex == 0){
					console.log("---------Not Commit-->>>>>>>");
					this.StoreGoodsBusinessInData();
				} else {
					console.log("---------Has Commit-->>>>>>>");
					this.StoreGoodsBusinessInDataCommit();
				}
			}, 
			
			onShowDatePicker(type){//显示
				this.BillDatetype = type;
				this.showPicker = true;
				this.BillDatevalue = this[type];
			},
			
			BillDateConfirm(e) {//选择
				this.showPicker = false;
				if(e) {
					this[this.BillDatetype] = e.value; 
					this.BillBeginDate = e.value[0];
					this.BillEndDate = e.value[1];
				}
			}, 

			StoreGoodsBusinessInData: function() {
				that.ItemDataList = [];
				let aCommitStatus = 0;
				if (this.activeTabIndex  == 0) {
					aCommitStatus = 0
				}
				else if (this.activeTabIndex  == 1) {
					aCommitStatus = 1
				};

				console.log("000000000000---" + this.searchBillNo);
				
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsBusinessInDataSQL',
							params: [{
								name: 'CommitStatus',
								value: '0'
							}, {
								name: 'BillNo',
								value: '%'+this.searchBillNo+'%'
							}, {
								name: 'LOGINID',
								value: getApp().globalData.LoginID
							}, {
								name: 'PLANLOGINID',
								value: getApp().globalData.LoginID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						that.ItemDataList = that.ItemDataList.concat(data);
					},
				})
			},
			
			StoreGoodsBusinessInDataCommit: function() {
				that.ItemDataList = [];
				let aCommitStatus = 0;
				if (this.activeTabIndex  == 0) {
					aCommitStatus = 0
				}
				else if (this.activeTabIndex  == 1) {
					aCommitStatus = 1
				};
			
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsBusinessInDataCommitSQL',
							params: [{
								name: 'CommitStatus',
								value: '1'
							},{
								name: 'BeginDate',
								value: this.BillBeginDate
							},{
								name: 'EndDate',
								value: this.BillEndDate
							},{
								name: 'BillNo',
								value: '%'+this.searchBillNo+'%'
							},{
								name: 'LOGINID',
								value: getApp().globalData.LoginID
							},{
								name: 'PLANLOGINID',
								value: getApp().globalData.LoginID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						that.ItemDataList = that.ItemDataList.concat(data);
					},
				})
			},
			
			// 卡片点击方法
			cardClickFun: function(item, index) {
				uni.navigateTo({
					url: '/pages/storegoods/storeGoodsBusinessInDetail?billid=' + item.BillMasterID
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}
	
	.test{
	    text-align: center;
	    padding: 10px 0;
	}
	button{
	    margin: 20upx;
	    font-size: 28upx;
	}
</style>
