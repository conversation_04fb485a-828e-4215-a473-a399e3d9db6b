<template>
	<view>
		<u-sticky>
		    <div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
		        <u-tabs :list="tabList" name="title" active-color="red" :is-scroll="false" 
				@change="onTabChange"></u-tabs>
		    </div>
		</u-sticky>
		
		<view class="u-demo-block">
			<view class="u-page__tag-item">
				<u-search v-model="searchProductWeaveNo" :show-action="false" @custom="selectProductWeaveData"
					@search="selectProductWeaveData" placeholder="请输入排产单号或织厂名称"></u-search>
			</view>
		</view>
		
		<dataNull v-if="list.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关排产单" title1="请添加或者更换搜索添加">
		</dataNull>
		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" @scrolltolower="selectDataFun"
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore">
			<view v-for="(item, index) in list" :key="index" @click="cardClickFun(item, index)">
				<productweaveview :item="item" :isSelect="isSelect" :index="index"></productweaveview>
			</view>
			<getMore :isMore="isMore"></getMore>
		</scroll-view>
	</view>
</template>

<script>
	let that = '';
	//import {tyDropDown,dropDownByUser,bjdDatePxData,bjdSxDropDownData} from '../../static/utils/dropdown.js'
	//import {getDayFun,getDayByNumFun} from '@/static/utils/date.js'
	import dataNull from '@/components/dataNull/dataNull.vue'
	import addBtn from '@/components/addBtn/addBtn.vue'
	import productweaveview from '@/pages/productorder/productweaveview.vue'
	import getMore from '@/components/getMore/getMore.vue'
	import topDropdown from '../../components/topDropdown/topDropdown.vue'
	import util, {playSuccessAudio,playErrorAudio} from '../../common/util';
	import {TbOrderCouponService} from "@/dev/services/tb-order-coupon.service";
	export default {
		components: {
			dataNull,
			addBtn,
			getMore,
			productweaveview,
			topDropdown
		},
		data() {
			return {
				tabList: [
				    {title: '现货', status: null},
				    {title: '版单', status: '1'},
				    {title: '全部', status: '2'},
				],
				searchProductWeaveNo: '',
				activeTabIndex: 0,
				sxList: [],
				list: [],
				pageIndex: 1,
				isMore: true,
				scrollHeight: '667px',
				triggered: false,
				isSelect: false,
				pageType: '',
				sortObj: {
					update_date: -1
				},
				optionsReq: {}, // 第一个下拉框请求参数
				dateReq: [], // 日期相关请求参数
				sxReq: JSON.stringify({}), // 筛选想请求参数
				matchObj: {},
				searchValue: '',
				clientIdArr: [],
				tabNoEqualArr: [], //标签页不等于数据
				depId: undefined
			}
		},
		onLoad(e) {
			that = this;
			let obj = {};

			if (e.clientId) {
				that.clientIdArr = [e.clientId]
			}
			that.matchObj = obj;
			that.optionsReq = JSON.stringify(obj);
			that.pageType = e.type ? e.type : '';
			if (e.type) {
				that.isSelect = true;
			}
			uni.getSystemInfo({
				success(res) {
					that.scrollHeight = res.windowHeight - 40 + 'px';
				}
			})
			if (e.next_gjDate) {
				let dateArr = getDayByNumFun(e.next_gjDate);
				let dateObj = {
					field: 'next_gjDate',
					sTime: dateArr[1],
					eTime: dateArr[0]
				};
				let arr = that.sxList;
				//arr[0].current = 0;
				that.sxList = arr;
				that.dateReq.push(dateObj)
			} else {
				let arr = that.sxList;
				//arr[0].current = '';
				that.sxList = arr;
			}
			that.selectDataFun();
			//uni.$on('deleteCardFun', that.deleteCardFun);
			//uni.$on('updateBjdListByIndex', that.updateBjdListByIndex)
			//uni.$on('addBjdItemInListFun', that.addBjdItemInListFun)
		},
		watch: {
		    activeTabIndex: {
		        deep: true, // 深度监听
		        handler(newVal, oldVal) {
		            //console.log("activeTabIndex参数改变，即将刷新...", `新值：${newVal}`, `旧值：${oldVal}`);
		            that.selectDataFun(newVal);
					//this.doRefresh(this.tbOrderCouponList)
		        }
		    }
		},
		// 上拉加载
		onReachBottom: function () {
			/*
		    if (this.pagination.hasNextPage) {
		        this.doInfinite()
		    }
			*/
		},
		onBackPress() {
			uni.$off('deleteCardFun', that.deleteCardFun);
			uni.$off('updateBjdListByIndex', that.updateBjdListByIndex)
			uni.$off('addBjdItemInListFun', that.addBjdItemInListFun);
		},
		methods: {
			onTabChange(index) {
			    const _self = this;
			    this.activeTabIndex = index;
			    //this.urlParams.activeTabIndex = this.activeTabIndex;
			},
			
			selectProductWeaveData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetProductWeaveSQL',
							params: [{
								name: 'WFID',
								value: 0
							}, {
								name: 'MWFID',
								value: 0
							}, {
								name: 'ProductWeaveNo',
								value: '%' + this.searchProductWeaveNo + '%'
							}, {
								name: 'WeaveFactoryName',
								value: '%' + this.searchProductWeaveNo + '%'
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						if (that.pageIndex == 1) {
							that.list = [];
						}
						if (res.data == 20) {
							that.pageIndex += 1;
							that.isMore = true;
						} else {
							that.isMore = false;
						}
						that.list = that.list.concat(data);
					},
				})
			},
			
			/**
			 * 加载更多数据中
			 * @param infiniteScroll
			 */
			doInfinite(infiniteScroll) {
			    const _self = this;
			    new TbOrderCouponService().findTableTbOrderCouponList({
			        pagination: _self.pagination,
			        data: {
			            communityId: _self.globalVariable.userInfo.tbCommunity.id,
			            userId: _self.globalVariable.userInfo.id,
			            status: _self.tabList[_self.activeTabIndex].status,
			            orderBy: 'create_time desc',
			        },
			    }).then(result => {
			        _self.pagination.refreshPage(result);
			        _self.tbOrderCouponList = [..._self.tbOrderCouponList, ...result.list];
			    });
			},
			
			// 查询报价单
			selectDataFun: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetProductWeaveSQL',
							params: [{
								name: 'WFID',
								value: 0
							}, {
								name: 'MWFID',
								value: 0
							}, {
								name: 'ProductWeaveNo',
								value: '%' + this.searchProductWeaveNo + '%'
							}, {
								name: 'WeaveFactoryName',
								value: '%' + this.searchProductWeaveNo + '%'
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						if (that.pageIndex == 1) {
							that.list = [];
						}
						if (res.data == 20) {
							that.pageIndex += 1;
							that.isMore = true;
						} else {
							that.isMore = false;
						}
						that.list = that.list.concat(data);
					},
				})
			},
			// 卡片点击方法
			cardClickFun: function(item, index) {
				uni.navigateTo({
					url: '/pages/productorder/productweaveDetail?'
						+'billmid=' + item.ProductWeaveMasterID
						+'&billdid=' + item.ProductWeaveDetailID
				})
			},
			
			// 搜索框回调方法
			searchBoxEmitFun: function(e) {
				that.searchValue = e.searchValue1;
				that.cxGetDataFun()
			},
			// 下拉刷新
			onRefresh: function() {
				if (that.triggered) return
				that.triggered = true;
				that.cxGetDataFun();
			},
			onRestore: function(e) {
				that.triggered = false; // 需要重置
			},
			// 重新获取数据
			cxGetDataFun: function() {
				that.pageIndex = 1;
				that.isMore = true;
				that.selectDataFun();
			},
			deleteCardFun: function(e) {
				let arr = that.list;
				arr.splice(e.index, 1);
				that.list = arr;
			},
			// add页面新增数据
			addBjdItemInListFun: function(e) {
				that.cxGetDataFun()
			},
			// 通过下标更新list数据
			updateBjdListByIndex: function(e) {
				that.$set(that.list, parseInt(e.index), e.obj)
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}
</style>
