<template>
	<u-form ref="uForm">
		<u-form-item label-width="80" label="品名:">
			<input type="text" v-model="FabricGoodsName"  style="width:100px;" />
			<text >颜色：</text>
			<input type="text" v-model="GoodsCodeName" style="width:100px;" />
			<button class="mini-btn" type="primary" size="mini" @tap="GetDetailListData">查询</button>
		</u-form-item>
		<view>
		    <lyy-table headerFixed :contents="contents" :columnFixed="2" :headers="headers" :totalRow="totalRow"
		        @rowClick="rowClick" @onPullup="pullup"></lyy-table>
		</view>

	</u-form>

</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
    export default {
        name: "btable",
        data() {
            return {
				FabricGoodsName:'',
				GoodsCodeName:'',
                loading: false,
                totalRow: ['Roll', 'Qty', 'UseRoll'],
                headers: [{
                    label: '编号',
                    key: 'FabricGoodsNo',
                    width: this.upx2px(200)
                }, {
                    label: '品名',
                    key: 'FabricGoodsName',
                    width:  this.upx2px(150)
                }, {
                    label: '色号',
                    key: 'GoodsCodeNo',
                    width:  this.upx2px(150)
                }, {
                    label: '颜色',
                    key: 'GoodsCodeName',
                    width:  this.upx2px(150),
                }, {
                    label: '单位',
                    key: 'UnitName',
                    width:  this.upx2px(100),
                },, {
                    label: '仓库',
                    key: 'StoreName',
                    width:  this.upx2px(150),
                },{
                    label: '条数',
                    key: 'Roll',
                    width:  this.upx2px(100),
                }, {
                    label: '重量',
                    key: 'Qty',
                    width:  this.upx2px(200),
                }, {
                    label: '可用',
                    key: 'UseRoll',
                    width:  this.upx2px(100),
                }],
                contents: [],
            }
        },
        methods: {
            upx2px(value) {
                //#ifndef MP-WEIXIN
                return uni.upx2px(value) + 'px'
                //#endif
                //#ifdef MP-WEIXIN
                return uni.upx2px(value)
                //#endif
            },
            rowClick(e) {
                console.log(e)
            },
            pullup(){
                console.log('上拉')
            },
			GetDetailListData: function() {
				this.CarryDetailList = [];
				this.BillID = 0;
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreGoodsMasterSQL',
							params: [{
								name: 'FGNo',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'FGName',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'GCNO',
								value: '%'+this.GoodsCodeName+'%'
							},{
								name: 'GCName',
								value: '%'+this.GoodsCodeName+'%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.contents = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.contents.push(aResultData[i]);
							};

						} else if (res.data.status == 0 && res.data.count <= 0) {
							console.log('-->出错')
						} else {
							console.log('-->出错')
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
        }
    }
</script>

<style lang="scss" scoped>
    .uni-progress {
        color: red;

        ::v-deep( .uni-progress-info) {
            font-size: 10px !important;
        }
    }
</style>
