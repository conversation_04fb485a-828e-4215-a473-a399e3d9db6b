<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label="仓库名称" label-width="150" prop="StoreName">
				<u-input :border="border" type="select" :select-open="actionSheetShow" v-model="StoreName"
					placeholder="请选择仓库" @click="actionSheetShow = true"></u-input>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearStoreNameData">清空</button>
			</u-form-item>
			<u-form-item>
				<u-search shape="square" v-model="FabricGoodsName" placeholder="成品名称" actionText=""></u-search>
				<u-search shape="square" v-model="GoodsCodeName" placeholder="成品色号" actionText=""></u-search>
			</u-form-item>
			<u-form-item>
				<u-search shape="square" v-model="CrockNo" placeholder="缸号" actionText=""></u-search>
				<button class="mini-btn" type="primary" size="mini" @tap="GetGoodsDetailData">查询</button>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">总条数：{{GoodsSumRoll}}</text>
				<text class="title" style="width:200px;">总重量：{{GoodsSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="GoodsDataList" @onCellClick="GetGoodsDetailData"	/>
		<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet>
	</view>
</template>

<script>
	import util, {parFabricGoodsBarCode2D} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
    export default {
        name: "btable",
        data() {
            return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				StoreName:'',
				FabricGoodsName:'',
				GoodsCodeName:'',
				CrockNo:'',
				GoodsSumRoll:0,
				GoodsSumQty:0,
                GoodsDataList:[],
				StoreNameDataList: [],
				headersMaster: [{
					                label: '单据日期',
					                key: 'BillDate'
					            }, {
					                label: '单据类型',
					                key: 'BillTypeName'
					            }, {
					                label: '往来单位',
					                key: 'CustomerName'
					            }, {
					                label: '成品编号',
					                key: 'FabricGoodsNo'
					            }, {
					                label: '成品名称',
					                key: 'FabricGoodsName'
					            }, {
					                label: '成品色号',
					                key: 'GoodsCodeNo'
					            }, {
					                label: '成品颜色',
					                key: 'GoodsCodeName'
					            }, {
					                label: '缸号',
					                key: 'CrockNo'
					            }, {
					                label: '条数',
					                key: 'Roll'
					            }, {
					                label: '重量',
					                key: 'Qty'
					            }, {
					                label: '备注',
					                key: 'Remark'
					            }],

				}
        },
		onLoad() {
			this.getStoreNameData();
		},
        methods: {
            upx2px(value) {
                //#ifndef MP-WEIXIN
                return uni.upx2px(value) + 'px'
                //#endif
                //#ifdef MP-WEIXIN
                return uni.upx2px(value)
                //#endif
            },
            rowClick(e) {
                console.log(e)
            },
            pullup(){
                console.log('上拉')
            },
			// 点击actionSheet回调
			actionSheetCallback(index) {
				uni.hideKeyboard();
				this.StoreName = this.StoreNameDataList[index].text;
			},

			ClearStoreNameData() {
				this.StoreName = '';
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricGoods%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.StoreNameDataList = res.data.data;
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetGoodsDetailData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreGoodsAllOutDetailSQL',
							params: [{
								name: 'SName',
								value: '%'+this.StoreName+'%'
							},{
								name: 'FGNo',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'FGName',
								value: '%'+this.FabricGoodsName+'%'
							},{
								name: 'GCNO',
								value: '%'+this.GoodsCodeNo+'%'
							},{
								name: 'GCName',
								value: '%'+this.GoodsCodeName+'%'
							},{
								name: 'CrockNo',
								value: '%'+this.CrockNo+'%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.GoodsDataList = res.data.data;
							var aResultData = res.data.data;
							this.GoodsSumRoll = 0;
							this.GoodsSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								this.GoodsSumRoll = this.GoodsSumRoll + aResultData[i].Roll;
								this.GoodsSumQty = this.GoodsSumQty + aResultData[i].Qty;
							};
							this.GoodsSumRoll = this.GoodsSumRoll.toFixed(2) + '条';
							this.GoodsSumQty = this.GoodsSumQty.toFixed(2) + 'Kg';
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.GoodsDataList = [];
						} else {
							this.GoodsDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
        },
    }
</script>

<style lang="scss" scoped>
    .uni-progress {
        color: red;

        ::v-deep( .uni-progress-info) {
            font-size: 10px !important;
        }
    }
</style>
