<template>
	<view>
		<u-sticky>
		    <div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
		        <u-tabs :list="tabList" name="title" active-color="red" :is-scroll="false" 
				:current="activeTabIndex" @change="onTabChange"></u-tabs>
		    </div>
		</u-sticky>
		<dataNull v-if="ItemDataList.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关出仓资料" title1="请添加或者更换搜索添加">
		</dataNull>
		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" 
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray">
			<view v-for="(item, index) in ItemDataList" :key="index" @click="cardClickFun(item, index)">
				<BusinessCheckItem :item="item" :isSelect="isSelect" :index="index"></BusinessCheckItem>
			</view>
		</scroll-view>
		<addBtn url="./storeYarnBusinessCheckAdd"></addBtn>
	</view>
</template>

<script>
	let that = '';
	import addBtn from '@/components/addBtn/addBtn.vue'
	import BusinessCheckItem from '@/components/card/storeYarnBusinessCheckItem.vue';
	import util from '../../common/util';
	export default {
		components: {
			BusinessCheckItem
		},
		data() {
			return {
				tabList: [
				    {title: '未审核', status: '0'},
				    {title: '已审核', status: '1'},
				],
				activeTabIndex: 0,
				ItemDataList: [],
				pageIndex: 1,
				isMore: true,
				scrollHeight: '667px',
				triggered: false,
				isSelect: false,
			}
		},
		onLoad(e) {
			that = this;
			uni.getSystemInfo({
				success(res) {
					that.scrollHeight = res.windowHeight - 40 + 'px';
				}
			})
			that.StoreYarnBusinessCheckData();
		},
		/* watch: {
		    activeTabIndex: {
		        deep: true, // 深度监听
		        handler(newVal, oldVal) {
		            //console.log("activeTabIndex参数改变，即将刷新...", `新值：${newVal}`, `旧值：${oldVal}`);
		            that.StoreYarnBusinessInData(newVal);
					//this.doRefresh(this.tbOrderCouponList)
		        }
		    }
		}, */
		// 上拉加载
		onReachBottom: function () {
			/*
		    if (this.pagination.hasNextPage) {
		        this.doInfinite()
		    }
			*/
		},
		onBackPress() {
			uni.$off('deleteCardFun', that.deleteCardFun);
			uni.$off('updateBjdListByIndex', that.updateBjdListByIndex)
			uni.$off('addBjdItemInListFun', that.addBjdItemInListFun);
		},
		methods: {
			onTabChange(index) {
			    const _self = this;
			    this.activeTabIndex = index;
			    //this.urlParams.activeTabIndex = this.activeTabIndex;
			},

			StoreYarnBusinessCheckData: function() {
				let aCommitStatus = 0;
				if (this.activeTabIndex  == 0) {
					aCommitStatus = 0
				}
				else if (this.activeTabIndex  == 1) {
					aCommitStatus = 1
				};

				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreYarnBusinessCheckDataSQL',
							params: [{
								name: 'CommitStatus',
								value: aCommitStatus
							}, {
								name: 'LOGINID',
								value: getApp().globalData.LoginID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						that.ItemDataList = that.ItemDataList.concat(data);
					},
				})
			},
			
			// 卡片点击方法
			cardClickFun: function(item, index) {
				uni.navigateTo({
					url: '/pages/storeyarn/storeYarnBusinessCheckDetail?billid=' + item.BillMasterID
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}
</style>
