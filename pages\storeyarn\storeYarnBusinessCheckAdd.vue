<template>
	<view>
		<u-form ref="uForm">
			<view class="flex-white-plr26 ptb10 mt32 bdb_f5">
				<text class="mr26">盘点凭证</text>
				<u-input disabled placeholder="自动生成" v-model="CheckBillNo" />
				<text class="mr26">盘点日期</text> {{$u.timeFormat(CheckBillDate, 'yyyy-mm-dd')}}
			</view>
			<view @click="pickerSelectFun('仓库名称')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">仓库名称<text class="redXingh">*</text></text>
				<view :class="StoreName ? '' : 'cBlack'"> {{StoreName ? StoreName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view @click="pickerSelectFun('单据类型')" class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26">单据类型<text class="redXingh">*</text></text>
				<view :class="BillTypeName ? '' : 'cBlack'"> {{BillTypeName ? BillTypeName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view class="flex-white-plr26 ptb20 bdb_f5" @click="selectCustomer">
				<text class="mr26">往来单位<text class="redXingh">*</text></text>
				<view :class="CustomerName ? '' : 'cBlack'"> {{CustomerName ? CustomerName : '请选择'}}
					<u-icon class="ml26" name="arrow-right" size="40" color="#888888"></u-icon>
				</view>
			</view>
			<view class="flex-white-plr26 ptb20 bdb_f5">
				<text class="mr26" >仓位编号<text class="redXingh">*</text></text>
				<input type="text" v-model="StoreStationNo" :focus="testFocus0" style="width:100px;" @confirm="GetStoreStationName" />
				<text class="title" style="width:150px;">仓位名称：{{StoreStationName}}</text>
			</view>
			<view class="flex-white-plr26-column ptb20 mt32">
				<view style="margin-bottom: 8rpx;">
					<text>备注内容</text>
				</view>
				<u-input v-model="Remark" type="textarea" :border="true" :height="100" :auto-height="true" />
			</view>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" :focus="testFocus1" style="width:250px;" @confirm="CheckBillDetailScan" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料名称：{{YarnName}}</text>
				<text class="title" style="width:200px;">原料缸号：{{YarnCrockNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料色号：{{YarnColorNo}}</text>
				<text class="title" style="width:200px;">原料颜色：{{YarnColorName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸盘前：{{YarnCrockNoOldRoll}}件</text>
				<text class="title" style="width:200px;">本缸盘前：{{YarnCrockNoOldQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸实盘：{{YarnCrockNoNewRoll}}件</text>
				<text class="title" style="width:200px;">本缸实盘：{{YarnCrockNoNewQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本缸盈亏：{{YarnCrockNoSumRoll}}件</text>
				<text class="title" style="width:200px;">本缸盈亏：{{YarnCrockNoSumQty}}Kg</text>
			</u-form-item>

			<u-form-item>
				<text class="title" style="width:200px;">本单盘前：{{YarnOldRoll}}件</text>
				<text class="title" style="width:200px;">本单盘前：{{YarnOldQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单实盘：{{YarnNewRoll}}件</text>
				<text class="title" style="width:200px;">本单实盘：{{YarnNewQty}}Kg</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">本单盈亏：{{YarnSumRoll}}件</text>
				<text class="title" style="width:200px;">本单盈亏：{{YarnSumQty}}Kg</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="CheckDetailList"
				@onCellClick="GetDetailCrockNoListData" />
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>

		<!--提交按钮-->
		<view class="submitView">
			<u-button type="primary" class="submitBtn" :ripple="true" ripple-bg-color="#909399" @click="submitBtnFun">
				{{pageType ? '保存' : '提交'}}
			</u-button>
		</view>
		<!--组件-->
		<!-- <u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow" @click="actionSheetCallback"></u-action-sheet> -->
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>

	</view>
</template>

<script>
	let that = '';
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				selectShow: false,
				selectList: [],
				selectType: '',
				pageType: '',
				testFocus0: true,
				testFocus1: false,
				CheckBillID: 0,
				CheckBillNo: '',
				CheckBillDate: '',
				StoreNameID: '',
				StoreName: '',
				CustomerID: 0,
				CustomerName: '',
				PlanDepartmentID: 0,
				PlanDepartmentName: '',
				SaleUserID: 0,
				SaleUserName: '',
				SaleCustomerAddress: '',
				StoreStationNo: '',
				StoreStationName: '',
				BillBusinessID: 0,
				BillTypeName: '',
				actionSheetShow: false,
				Remark: '',
				CheckDetailList:[],
				StoreNameDataList: [],
				BillBusinessDataList: [],
				YarnDataList: [],
				QRBarCode: '',
				YarnName: '',
				YarnColorNo: '',
				YarnColorName: '',
				YarnCrockNo: '',
				YarnCrockNoOldRoll: 0,
				YarnCrockNoOldQty: 0,
				YarnCrockNoNewRoll: 0,
				YarnCrockNoNewQty: 0,
				YarnCrockNoSumRoll: 0,
				YarnCrockNoSumQty: 0,
				YarnOldRoll: 0,
				YarnOldQty: 0,
				YarnNewRoll: 0,
				YarnNewQty: 0,
				YarnSumRoll: 0,
				YarnSumQty: 0,
				BillDataMessage: '',
				headersMaster: [{
					label: '原料名称',
					key: 'YarnName'
				}, {
					label: '色号',
					key: 'YarnColorNo'
				}, {
					label: '颜色',
					key: 'YarnColorName'
				}, {
					label: '原料缸号',
					key: 'YarnCrockNo'
				}, {
					label: '盘前件数',
					key: 'OldRoll'
				}, {
					label: '盘前重量',
					key: 'OldQty'
				}, {
					label: '实盘件数',
					key: 'NewRoll'
				}, {
					label: '实盘重量',
					key: 'NewQty'
				},{
					label: '盈亏件数',
					key: 'Roll'
				}, {
					label: '盈亏重量',
					key: 'Qty'
				}],
			}
		},
		watch: {
			cpList: function(val, old) {
				that.cpCalcFun();
			}
		},
		onLoad(e) {
			that = this;
			this.getStoreNameData();

			setTimeout(() => {
				this.getBillBusinessData();
			}, 500);

			uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$on('chanpinBindFun', that.chanpinBindFun)
			uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$on('shangjiBindFun', that.shangjiBindFun)
		},

		onBackPress() {
			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$off('chanpinBindFun', that.chanpinBindFun)
			uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$off('shangjiBindFun', that.shangjiBindFun)
		},
		methods: {

			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				console.log("---->>>" + str);
				that.selectList = [];
				if (str == '仓库名称') {
					that.selectList = this.StoreNameDataList;
				} else if (str == '单据类型') {
					that.selectList = this.BillBusinessDataList;
				}

				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '仓库名称') {
					that.StoreNameID = e[0].value;
					that.StoreName = e[0].label;
				} else if (that.selectType == '单据类型') {
					that.BillBusinessID = e[0].value;
					that.BillTypeName = e[0].label;
				}
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreYarnEmbryo%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			getBillBusinessData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetBillBusinessList',
							params: [{
								name: 'TypeStatus',
								value: '3'
							}, {
								name: 'TypeNo',
								value: 'BusinessTypeStoreYarn'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BillBusinessDataList.push({
									value: aResultData[i].BillBusinessID,
									label: aResultData[i].BillTypeName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.BillBusinessDataList = [];
						} else {
							this.BillBusinessDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			StoreYarnBusinessCheckDetail: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreYarnBusinessCheckDetailSQL',
							params: [{
								name: 'BillID',
								value: this.CheckBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.CheckDetailList = res.data.data;
							this.CheckBillNo = aResultData[0].BillNo;
							this.CheckBillDate = aResultData[0].BillDate;
							this.CustomerName = aResultData[0].CustomerName;
							this.StoreName = aResultData[0].StoreName;
							this.StoreStationNo = aResultData[0].StoreStationNo;
							this.StoreStationName = aResultData[0].StoreStationName;
							//this.YarnCrockNoOldRoll = 0;
							//this.YarnCrockNoOldQty = 0;
							//this.YarnCrockNoNewRoll = 0;
							//this.YarnCrockNoNewQty = 0;
							//this.YarnCrockNoSumRoll = 0;
							//this.YarnCrockNoSumQty = 0;
							this.YarnOldRoll = 0;
							this.YarnOldQty = 0;
							this.YarnNewRoll = 0;
							this.YarnNewQty = 0;
							this.YarnSumRoll = 0;
							this.YarnSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].OldRoll) != 0) {
									this.YarnOldRoll = this.YarnOldRoll + aResultData[i].OldRoll;
								}
								if (parseFloat(aResultData[i].OldQty) != 0) {
									this.YarnOldQty = this.YarnOldQty + aResultData[i].OldQty;
								}
								if (parseFloat(aResultData[i].NewRoll) != 0) {
									this.YarnNewRoll = this.YarnNewRoll + aResultData[i].NewRoll;
								}
								if (parseFloat(aResultData[i].NewQty) != 0) {
									this.YarnNewQty = this.YarnNewQty + aResultData[i].NewQty;
								}
								if (parseFloat(aResultData[i].Roll) != 0) {
									this.YarnSumRoll = this.YarnSumRoll + aResultData[i].Roll;
								}
								if (parseFloat(aResultData[i].Qty) != 0) {
									this.YarnSumQty = this.YarnSumQty + aResultData[i].Qty;
								}
							};
							this.YarnOldRoll = this.YarnOldRoll.toFixed(2);
							this.YarnOldQty = this.YarnOldQty.toFixed(2);
							this.YarnNewRoll = this.YarnNewRoll.toFixed(2);
							this.YarnNewQty = this.YarnNewQty.toFixed(2);
							this.YarnSumRoll = this.YarnSumRoll.toFixed(2);
							this.YarnSumQty = this.YarnSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.CheckDetailList = [];
						} else {
							this.CheckDetailList = [];
						}

					},
				})
			},

			// 日期修改
			bindDateChange: function(e) {
				that.bjdDate = e.detail.value;
				that.bjdDateTime = new Date(e.detail.value + ' 00:00:00').getTime()
			},
			// 展示相应数据选择框


			scanFun: function() {
				uni.scanCode({
					success(res) {
						that.code = res.result;
					}
				})
			},
			// 选择所属客户
			selectCustomer: function() {
				uni.navigateTo({
					url: '../basedata/customer/customer?type=织厂'
				})
			},
			// 绑定客户
			bjdKehuBindFun: function(e) {
				console.log("CustomerName===" + e.CustomerName);
				that.CustomerID = e.CustomerID;
				that.CustomerName = e.CustomerName;
				that.PlanDepartmentID = e.PlanDepartmentID;
				that.PlanDepartmentName = e.PlanDepartmentName;
				that.SaleUserID = e.SaleUserID;
				that.SaleUserName = e.SaleUserName;
				that.SaleCustomerAddress = e.CustomerAddress + ' ' + e.CustomerPhone + ' ' + e.CustomerLinkName;

				/*
				that.clientId = e.clientId;
				that.clientName = e.clientName;
				*/
			},

			// 提交按钮方法
			submitBtnFun: function() {
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				if (this.BillTypeName == '') {
					this.playError();
					this.BillDataMessage = '单据类型不能为空，请先输入单据类型！';
					return;
				}
				if (this.CustomerName == '') {
					this.playError();
					this.BillDataMessage = '往来单位不能为空，请先输入往来单位！';
					return;
				}

				if (this.StoreStationName == '') {
					this.playError();
					this.BillDataMessage = '仓位名称不能为空，请先输入仓位编号！';
					return;
				}

				if (this.CheckBillID > 0) {
					this.playError();
					this.BillDataMessage = '当前单据已经提交，不能重复提交！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreYarnCreateCheckBillMaster',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.CheckBillID
								},
								{
									name: '@BillBusinessID',
									value: this.BillBusinessID
								},
								{
									name: '@StoreNameID',
									value: this.StoreNameID
								},
								{
									name: '@CustomerID',
									value: this.CustomerID
								},
								{
									name: '@StoreStationNo',
									value: this.StoreStationNo
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@Remark',
									value: this.Remark
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.CheckBillID = aResultData.CheckStoreBillID;
								this.CheckBillNo = aResultData.CheckStoreBillNo;
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '提交出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = '提交出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			CheckBillDetailScan() {
				if (this.StoreNameID == 0 && this.CheckBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}
				var aStrBarCode = parYarnGoodsBarCode2D(this.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				if (aStrBarCode == '') {
					this.playError();
					this.QRBarCode = '';
					this.testFocus1 = true;
					this.BillDataMessage = '无效的二维码！';
					return;
				}
				var aBarCodeList = aStrBarCode.split(',');
				this.DyeWorksDigCode = aBarCodeList[0];
				this.YarnGoodsCodeNo = aBarCodeList[2];
				this.YarnGoodsColorNo = aBarCodeList[3];
				this.YarnCrockNo = aBarCodeList[5];
				this.YarnBoxBillNo = aBarCodeList[6];
				this.YarnBoxRoll = aBarCodeList[7];
				this.YarnGrossQty = aBarCodeList[8];
				this.YarnNetQty = aBarCodeList[9];
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreYarnCreateCheckBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.CheckBillID
								},
								{
									name: '@StoreNameID',
									value: this.StoreNameID
								},
								{
									name: '@StoreStationNo',
									value: this.StoreStationNo
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@QRBarCode',
									value: aStrBarCode
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.YarnName = aResultData.YarnName;
								this.YarnColorNo = aResultData.YarnColorNo;
								this.YarnColorName = aResultData.YarnColorName;
								this.YarnCrockNo = aResultData.YarnCrockNo
								this.YarnCrockNoOldRoll = parseFloat(aResultData.YarnCrockNoOldRoll);
								this.YarnCrockNoOldQty = parseFloat(aResultData.YarnCrockNoOldQty);
								this.YarnCrockNoNewRoll = parseFloat(aResultData.YarnCrockNoNewRoll);
								this.YarnCrockNoNewQty = parseFloat(aResultData.YarnCrockNoNewQty);
								this.YarnCrockNoSumRoll = parseFloat(aResultData.YarnCrockNoSumRoll);
								this.YarnCrockNoSumQty = parseFloat(aResultData.YarnCrockNoSumQty);
								this.YarnOldRoll = parseFloat(aResultData.YarnOldRoll);
								this.YarnOldQty = parseFloat(aResultData.YarnOldQty);
								this.YarnNewRoll = parseFloat(aResultData.YarnNewRoll);
								this.YarnNewQty = parseFloat(aResultData.YarnNewQty);
								this.YarnSumRoll = parseFloat(aResultData.YarnSumRoll);
								this.YarnSumQty = parseFloat(aResultData.YarnSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.StoreYarnBusinessCheckDetail();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus1 = true;
							this.testFocus0 = false;
						});
						this.BillDataMessage = '盘点扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			addCzjlFun: function(action, _id) {
				let czjlObj = {
					create_date: new Date().getTime(),
					czRen: uni.$userInfo._id,
					pageType: 'quotation',
				}
				let xgInfoObj = that.xgInfoObj;
				czjlObj.dataId = xgInfoObj._id;
				czjlObj.type = '编辑';
				czjlObj.newUpdate = new Date().getTime();
				czjlObj.oldUpdate = xgInfoObj.update_date;
				let content = [];
				let str = '';
				if (that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if (that.sjName != xgInfoObj.sjName) {
					str = '修改 商机 ，由 "' + xgInfoObj.sjName + '" 变更为 "' + that.sjName + '"'
					content.push(str)
				}
				if (that.clientName != xgInfoObj.clientName) {
					str = '修改 客户 ，由 "' + xgInfoObj.clientName + '" 变更为 "' + that.clientName + '"'
					content.push(str)
				}
				if (that.bjName != xgInfoObj.bjName) {
					str = '修改 报价单名称 ，由 "' + xgInfoObj.bjName + '" 变更为 "' + that.bjName + '"'
					content.push(str)
				}
				if (that.clientLxrId != xgInfoObj.clientLxrId) {
					str = '修改 联系人 ，由 "' + xgInfoObj.clientLxr + '" 变更为 "' + that.clientLxr + '"'
					content.push(str)
				}
				if (that.bjPrice != xgInfoObj.bjPrice) {
					str = '修改 报价金额 ，由 "' + xgInfoObj.bjPrice + '" 变更为 "' + that.bjPrice + '"'
					content.push(str)
				}
				if (that.describe != xgInfoObj.describe) {
					str = '修改 备注 ，由 "' + xgInfoObj.describe + '" 变更为 "' + that.describe + '"'
					content.push(str)
				}
				if (that.bjdDate != xgInfoObj.bjdDate) {
					str = '修改 报价日期 ，由 "' + xgInfoObj.bjdDate + '" 变更为 "' + that.bjdDate + '"'
					content.push(str)
				}
				if (that.fuZeRen != xgInfoObj.fuZeRen) {
					str = '修改 负责人 ，由 "' + xgInfoObj.fuZeRen + '" 变更为 "' + that.fuZeRen + '"'
					content.push(str)
				}
				czjlObj.content = content;
				uni.$czjlApiAddFun(czjlObj);
			},
			// 数据恢复
			setDataFun: function() {
				let obj = uni.$infoObj
				that.xgInfoObj = uni.$infoObj
				that.bjName = obj.bjName
				that.sjName = obj.sjName
				that.sjId = obj.sjId
				that.clientName = obj.clientName
				that.clientId = obj.clientId
				that.clientLxr = obj.clientLxr
				that.clientLxrId = obj.clientLxrId
				that.bjPrice = obj.bjPrice
				that.describe = obj.describe
				that.bjdDate = obj.bjdDate
				that.bjdDateTime = obj.bjdDateTime
				// that.cpList = obj.cpList
				// that.cpHzObj = obj.cpHzObj || {}
				that.fuZeRenId = obj.fuZeRenId
				that.fuZeRen = obj.fuZeRen
				that.getCpByIdFun()
			},
			getCpByIdFun: function() {
				let reqData = {
					action: 'getCp',
					params: {
						bjdId: that.xgInfoObj._id
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmBaoJiaDanApi(reqData)
					.then(res => {
						let data = res.result.data;
						if (data.length > 0) {
							let cpHzObj = {
								discount: 0,
								price: 0,
								sumPrice: 0,
								totalNum: 0
							};
							for (var i = 0; i < data.length; i++) {
								var obj = data[i];
								obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
								obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
								data[i] = obj;
								cpHzObj.price += parseFloat(obj.price) * obj.num;
								cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
								cpHzObj.totalNum += obj.num;
							}
							cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
							that.cpHzObj = cpHzObj;
							that.cpList = data;
						}
					})
			},
			// 根据商机id查询产品
			getCpBysjIdFun: function(sjId) {
				let reqData = {
					action: 'getCp',
					params: {
						sjId: sjId
					}
				}
				uni.showLoading({
					title: '加载中...'
				})
				crmShangJiApi(reqData)
					.then(res => {
						let data = res.result.data;
						if (data.length > 0) {
							let cpHzObj = {
								discount: 0,
								price: 0,
								sumPrice: 0,
								totalNum: 0
							};
							for (var i = 0; i < data.length; i++) {
								var obj = data[i];
								obj._id = '';
								obj.discount = (obj.sellingPrice / obj.price * 100).toFixed(2);
								obj.totalPrice = (obj.sellingPrice * obj.num).toFixed(2);
								data[i] = obj;
								cpHzObj.price += parseFloat(obj.price) * obj.num;
								cpHzObj.sumPrice += parseFloat(obj.sellingPrice) * obj.num;
								cpHzObj.totalNum += obj.num;
							}
							cpHzObj.discount = cpHzObj.sumPrice / cpHzObj.price * 100;
							that.cpHzObj = cpHzObj;
							that.cpList = data;
						}
					})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
