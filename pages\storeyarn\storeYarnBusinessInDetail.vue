<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item>
				<text class="title" style="width:200px;">单号：{{BillNo}}</text>
				<text class="title" style="width:200px;">日期：{{BillDate}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">往来单位：{{CustomerName}}</text>
				<text class="title" style="width:200px;">仓库名称：{{StoreName}}</text>
			</u-form-item>

			<u-form-item label-width="150" label="扫描仓位:">
				<input type="text" v-model="StoreStationNo" :focus="testFocus0" style="width:80px;"
					@confirm="GetStoreStationName" />
				<text class="title" style="width:80px;">名称：{{StoreStationName}}</text>
			</u-form-item>
			<u-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="QRBarCode" :focus="testFocus1" style="width:250px;"
					@confirm="InBillDetailScan" />
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料名称：{{YarnName}}</text>
				<text class="title" style="width:200px;">原料缸号：{{YarnCrockNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">原料色号：{{YarnColorNo}}</text>
				<text class="title" style="width:200px;">原料颜色：{{YarnColorName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:120px;">共：{{YarnCrockNoSumRoll}}件</text>
				<text class="title" style="width:120px;">已扫：{{YarnCrockNoHasScanRoll}}件</text>
				<text class="title" style="width:120px;">未扫：{{YarnCrockNoNotScanRoll}}件</text>
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">总件数：{{YarnSumRoll}}</text>
				<text class="title" style="width:200px;">总重量：{{YarnSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="InDetailList"/>
			<!--	<wyb-table ref="table" :headers="headersDetail" :contents="GoodsCrockNoDataList"  height="600rpx" /> !-->
		</view>
		<!-- 		<u-action-sheet :list="InDetailList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet> -->
	</view>
</template>

<script>
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	let that = '';
	export default {
		data() {
			return {
				BillMasterID: 0,
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				QRBarCode: '',
				BillNo: '',
				BillDate: '',
				CustomerName: '',
				StoreNameID: 0,
				StoreName: '',
				StoreStationNo: '',
				StoreStationName: '',
				YarnName: '',
				YarnColorNo: '',
				YarnColorName: '',
				YarnCrockNo: '',
				testFocus0: true,
				testFocus1: false,
				YarnCrockNoSumRoll: 0,
				YarnCrockNoHasScanRoll: 0,
				YarnCrockNoNotScanRoll: 0,
				YarnSumRoll: 0,
				YarnSumQty: 0,
				InDetailList: [],
				BillDataMessage: '',
				headersMaster: [{
					label: '原料名称',
					key: 'YarnName'
				}, {
					label: '色号',
					key: 'YarnColorNo'
				}, {
					label: '颜色',
					key: 'YarnColorName'
				}, {
					label: '原料缸号',
					key: 'YarnCrockNo'
				}, {
					label: '件数',
					key: 'Roll'
				}, {
					label: '重量',
					key: 'Qty'
				}],
			}
		},

		onLoad(e) {
			that = this;
			if (e.billid) {
				that.BillMasterID = e.billid;
			}
			console.log("---BillID->" + this.BillMasterID);
			this.StoreYarnBusinessInDetail();
		},

		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
								this.StoreNameID = parseInt(aResultData[i].StoreNameID);
							};
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
							this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			InBillDetailScan() {
				this.testFocus1 = false;
				this.$nextTick(() => {
					this.testFocus0 = false;
					this.testFocus1 = true;
				});

				if (this.StoreStationName == '') {
					this.playError();
					this.BillDataMessage = '请先扫描仓位';
					return;
				}

				var aStrBarCode = parYarnGoodsBarCode2D(this.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				if (aStrBarCode == '') {
					this.playError();
					this.QRBarCode = '';
					this.testFocus2 = true;
					this.BillDataMessage = '无效的二维码！';
					return;
				}
				console.log("-------->" + aStrBarCode + "<----");
				var aBarCodeList = aStrBarCode.split(',');
				this.DyeWorksDigCode = aBarCodeList[0];
				this.YarnGoodsCodeNo = aBarCodeList[2];
				this.YarnGoodsColorNo = aBarCodeList[3];
				this.YarnCrockNo = aBarCodeList[5];
				this.YarnBoxBillNo = aBarCodeList[6];
				this.YarnBoxRoll = aBarCodeList[7];
				this.YarnGrossQty = aBarCodeList[8];
				this.YarnNetQty = aBarCodeList[9];
				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreYarnInBillDetailScan',
							method: 'open_proc',
							params: [{
									name: '@BillID',
									value: this.BillMasterID
								},
								{
									name: '@BillTypeID',
									value: '0'
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationNo
								},
								{
									name: '@QRBarCode',
									value: aStrBarCode
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.YarnName = aResultData.YarnName;
								this.YarnColorNo = aResultData.YarnColorNo;
								this.YarnColorName = aResultData.YarnColorName;
								this.YarnCrockNo = aResultData.YarnCrockNo;
								this.YarnCrockNoSumRoll = parseFloat(aResultData.YarnCrockNoSumRoll);
								this.YarnCrockNoHasScanRoll = parseFloat(aResultData.YarnCrockNoHasScanRoll);
								this.YarnCrockNoNotScanRoll = parseFloat(aResultData.YarnCrockNoNotScanRoll);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '入仓出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus0 = false;
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '入仓出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus0 = false;
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus0 = false;
							this.testFocus1 = true;
						});
						this.BillDataMessage = '入仓出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			StoreYarnBusinessInDetail: function() {
				if (this.BillMasterID == 0) {
					this.BillMasterID = 30685
				}
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreYarnBusinessInDetailSQL',
							params: [{
								name: 'BillID',
								value: this.BillMasterID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.InDetailList = res.data.data;
							this.BillNo = aResultData[0].BillNo;
							this.BillDate = aResultData[0].BillDate;
							this.CustomerName = aResultData[0].CustomerName;
							this.StoreName = aResultData[0].StoreName;
							this.YarnSumRoll = 0;
							this.YarnSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.YarnSumRoll = this.YarnSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.YarnSumQty = this.YarnSumQty + aResultData[i].Qty;
								}
							};
							this.YarnSumRoll = this.YarnSumRoll.toFixed(2) + '件';
							this.YarnSumQty = this.YarnSumQty.toFixed(2) + 'Kg';

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.InDetailList = [];
						} else {
							this.InDetailList = [];
						}

					},
				})
			},
		}
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
