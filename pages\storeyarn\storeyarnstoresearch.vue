<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label="仓库名称" label-width="150" prop="StoreName">
				<u-input :border="border" type="select" :select-open="actionSheetShow" v-model="StoreName"
					placeholder="请选择仓库" @click="actionSheetShow = true"></u-input>
				<button class="mini-btn" type="primary" size="mini" @tap="ClearBillData">清空</button>
			</u-form-item>
			<u-form-item>
				<u-search shape="square" v-model="YarnName" placeholder="原料名称" actionText=""></u-search>
				<u-search shape="square" v-model="YarnColorName" placeholder="原料色号" actionText=""></u-search>
				<button class="mini-btn" type="primary" size="mini" @tap="GetDetailListData">查询</button>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:200px;">总件数：{{YarnSumRoll}}</text>
				<text class="title" style="width:200px;">总重量：{{YarnSumQty}}</text>
			</u-form-item>
		</u-form>
		<view class="u-demo-area">
			<u-toast ref="uToast"></u-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="YarnDataList"	/>
		</view>
		<u-action-sheet :list="StoreNameDataList" v-model="actionSheetShow"  @click="actionSheetCallback"></u-action-sheet>
	</view>
</template>

<script>
	import util, {parFabricGoodsBarCode2D} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
    export default {
        name: "btable",
        data() {
            return {
				borderColor: '#e4e7ed',
				align: 'center',
				index: 0,
				actionSheetShow: false,
				StoreName:'',
				YarnName:'',
				YarnColorName:'',
				YarnSumRoll:0,
				YarnSumQty:0,
                YarnDataList:[],
				YarnCrockNoDataList:[],
				StoreNameDataList: [],
				headersMaster: [{
					                label: '名称',
					                key: 'YarnName'
					            }, {
					                label: '色号',
					                key: 'YarnColorNo'
					            }, {
					                label: '颜色',
					                key: 'YarnColorName'
					            }, {
					                label: '件数',
					                key: 'Roll'
					            }, {
					                label: '重量',
					                key: 'Qty'
					            }, {
					                label: '仓库',
					                key: 'StoreName'
					            }],
					headersDetail: [ {
					                label: '名称',
					                key: 'YarnName'
					            }, {
					                label: '色号',
					                key: 'YarnColorNo'
					            }, {
					                label: '颜色',
					                key: 'YarnColorName'
					            }, {
					                label: '缸号',
					                key: 'CrockNo'
					            }, {
					                label: '件数',
					                key: 'Roll'
					            }, {
					                label: '数量',
					                key: 'Qty'
					            }]

				}
        },
		onLoad() {
			this.getStoreNameData();
		},
        methods: {
            upx2px(value) {
                //#ifndef MP-WEIXIN
                return uni.upx2px(value) + 'px'
                //#endif
                //#ifdef MP-WEIXIN
                return uni.upx2px(value)
                //#endif
            },
            rowClick(e) {
                console.log(e)
            },
            pullup(){
                console.log('上拉')
            },
			// 点击actionSheet回调
			actionSheetCallback(index) {
				uni.hideKeyboard();
				this.StoreName = this.StoreNameDataList[index].text;
			},

			ClearStoreNameData() {
				this.StoreName = '';
			},

			ClearBillData: function() {
				this.StoreName = '',
				this.YarnName = '';
				this.YarnColorName = '';
				this.YarnCrockNo = '';
				this.YarnSumRoll = 0;
				this.YarnSumQty = 0;
				this.YarnDataList = [];
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreYarnEmbryo%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.StoreNameDataList = res.data.data;
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetDetailListData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreYarnMasterSQL',
							params: [{
								name: 'SName',
								value: '%'+this.StoreName+'%'
							},{
								name: 'FGNo',
								value: '%'+this.YarnName+'%'
							},{
								name: 'FGName',
								value: '%'+this.YarnName+'%'
							},{
								name: 'GCNO',
								value: '%'+this.YarnColorName+'%'
							},{
								name: 'GCName',
								value: '%'+this.YarnColorName+'%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.YarnDataList = res.data.data;
							var aResultData = res.data.data;
							this.YarnSumRoll = 0;
							this.YarnSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								this.YarnSumRoll = this.YarnSumRoll + aResultData[i].Roll;
								this.YarnSumQty = this.YarnSumQty + aResultData[i].Qty;
							};
							this.YarnSumRoll = this.YarnSumRoll.toFixed(2) + '件';
							this.YarnSumQty = this.YarnSumQty.toFixed(2) + 'Kg';
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.YarnDataList = [];
						} else {
							this.YarnDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
        },

		GetDetailCrockNoListData: function(index) {
			console.log(this.YarnDataList[index].YarnName);
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP_GetStoreGoodsDetailCrockNoSQL',
							params: [{
								name: 'StoreName',
								value: this.StoreName
							},{
								name: 'FGNo',
								value: this.YarnName
							},{
								name: 'GCNO',
								value: this.YarnColorName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.YarnCrockNoDataList = [];
							for (var i = 0; i < aResultData.length; i++) {
								this.YarnCrockNoDataList.push(aResultData[i]);
							};
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.YarnCrockNoDataList = [];
						} else {
							this.YarnCrockNoDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},
    }
</script>

<style lang="scss" scoped>
    .uni-progress {
        color: red;

        ::v-deep( .uni-progress-info) {
            font-size: 10px !important;
        }
    }
</style>
