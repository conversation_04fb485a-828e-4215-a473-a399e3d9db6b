<template>
	<view class="wrap">
		<!--<u-swiper :height="270" :list="imgList" :title="false" @click="imgListClick"></u-swiper>！-->
		<view class="workbench-title">销售管理</view>
		<view class="toolbar">
			<u-grid class="grid" :col="4" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/saleship/saleallocated')" >
					<view class="home-icon icon-color01">
						<i class="iconfont icon-qingjia"></i>
					</view>
					<view class="grid-text">销售预约</view>
				</u-grid-item>				
				<u-grid-item :index="1" @click="navTo('/pages/productorder/productweave')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-hetongguanli"></i>
					</view>
					<view class="grid-text">生产排产</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/saleship/salepickscan')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-hetongguanli"></i>
					</view>
					<view class="grid-text">成品配布</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/saleship/saleShipmentOut')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-hetongguanli"></i>
					</view>
					<view class="grid-text">销售付运</view>
				</u-grid-item>
			</u-grid>
		</view>
		<view class="workbench-title">成品管理</view>
		<view class="toolbar">
			<u-grid class="grid" :col="4" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/storegoods/dyeworksDyeback')" >
					<view class="home-icon icon-color04">
						<i class="iconfont icon-finance"></i>
					</view>
					<view class="grid-text">染整进仓</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('')">
					<view class="home-icon icon-color03">
						<i class="iconfont icon-mall-bag"></i>
					</view>
					<view class="grid-text">成品进仓</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('')">
					<view class="home-icon icon-color12">
						<i class="iconfont icon-baoxiaodan"></i>
					</view>
					<view class="grid-text">成品出仓</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">成品盘点</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">成品移架</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storegoods/storegoodsstoresearch')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">成品库存</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storegoods/storegoodsstoreallin')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">进仓查询</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storegoods/storegoodsstoreallout')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">出仓查询</view>
				</u-grid-item>
			</u-grid>
		</view>
		<view class="workbench-title">坯布管理</view>
		<view class="toolbar">
			<u-grid class="grid" :col="4" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/storefabric/storefabricInStore')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-hetongguanli"></i>
					</view>
					<view class="grid-text">坯布进仓</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/storefabric/storefabricOutStore')">
					<view class="home-icon icon-color03">
						<i class="iconfont icon-chucha"></i>
					</view>
					<view class="grid-text">坯布出仓</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('')">
					<view class="home-icon icon-color12">
						<i class="iconfont icon-ribao"></i>
					</view>
					<view class="grid-text">坯布盘点</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-tongzhi"></i>
					</view>
					<view class="grid-text">坯布移架</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoresearch')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">坯布库存</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoreallin')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">进仓查询</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storefabric/storefabricstoreallout')">
					<view class="home-icon icon-color05">
						<i class="iconfont icon-huiyishi"></i>
					</view>
					<view class="grid-text">出仓查询</view>
				</u-grid-item>
			</u-grid>
		</view>
		<view class="workbench-title">原料管理</view>
		<view class="toolbar">
			<u-grid class="grid" :col="4" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/storeyarn/storeYarnInStore')" >
					<view class="home-icon icon-color04">
						<i class="iconfont icon-yongche"></i>
					</view>
					<view class="grid-text">原料进仓</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storeyarn/storeYarnBusinessOut')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-shenpi"></i>
					</view>
					<view class="grid-text">原料出仓</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/storeyarn/storeYarnProduceWeave')">
					<view class="home-icon icon-color03">
						<i class="iconfont icon-jiabanshenqing"></i>
					</view>
					<view class="grid-text">织造配纱</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('/pages/storeyarn/storeYarnBusinessCheck')">
					<view class="home-icon icon-color12">
						<i class="iconfont icon-kaoqinchuqin"></i>
					</view>
					<view class="grid-text">原料盘点</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storeyarn/storeYarnMoveStore')">
					<view class="home-icon icon-color04">
						<i class="iconfont icon-haocaifei"></i>
					</view>
					<view class="grid-text">原料移架</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storeyarn/storeyarnstoresearch')">
					<view class="home-icon icon-color01">
						<i class="iconfont icon-gongwujiedai"></i>
					</view>
					<view class="grid-text">原料库存</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storeyarn/storeyarnstoreallin')">
					<view class="home-icon icon-color01">
						<i class="iconfont icon-gongwujiedai"></i>
					</view>
					<view class="grid-text">进仓查询</view>
				</u-grid-item>
				<u-grid-item  @click="navTo('/pages/storeyarn/storeyarnstoreallout')">
					<view class="home-icon icon-color01">
						<i class="iconfont icon-gongwujiedai"></i>
					</view>
					<view class="grid-text">出仓查询</view>
				</u-grid-item>
				<u-grid-item :index="0" @click="navTo('/pages/storeyarn/storeYarnBusinessIn')" >
					<view class="home-icon icon-color04">
						<i class="iconfont icon-yongche"></i>
					</view>
					<view class="grid-text">进仓上架</view>
				</u-grid-item>
			</u-grid>
		</view>
	</view>
	
</template>
<script>
	 import HeadNavBar from '@/components/headnavbar/index';
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
export default {
	components: {
	  HeadNavBar
	},
	data() {
		return {
			show: false,
			head: '/static/aidex/images/head.png',
			imgList: [
				{image: '/static/aidex/banner/banner01.png'}
				//{image: '/static/aidex/banner/banner02.png'}, 
				//{image: '/static/aidex/banner/banner03.png'}
			],
			todoCount: 3
		};
	},
	onLoad() {

	},
	onShow: function() {  

	} ,
	methods: {
		navTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		imgListClick(index) {
			console.log(`点击了第${index + 1}页图片`)
		},
		itemClick(index) {
			console.log(index);
		}
	}
};
</script>
<style lang="scss">
@import 'index.scss';
.banner-box{
	padding: 0 2%;
	width: 96%;
	height: 170rpx;
	margin: 30rpx 0 30rpx;
}
.u-swiper-wrap{
	padding:0 10px;
}

.banner-pic{
	width: 47%;
	float: left;
	display: inline-block;
	margin: 0 1.5%;
}
.banner-pic image{
	width: 100%;
	height: 170rpx;
	border-radius: 12rpx;
	box-shadow: 0px 0px 10px rgba(0,0,0,0.1);
}

.u-mode-light-info{
    background-color: #ffffff;
    color: #666666;
    border: 1px solid #e9ecf6;
	font-size: 12px;
	padding: 2px 8px;
	position: relative;
	top:-3px;
}
.workbench-title{
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	padding: 15px 30rpx;
}
 .home-icon i.icon-tongzhi{
	font-size: 22px;
 }
</style>
