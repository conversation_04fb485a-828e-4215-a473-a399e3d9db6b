<template>
  <view class="test-page">
    <view class="test-header">
      <text class="test-title">🎉 测试页面</text>
    </view>
    <view class="test-content">
      <text class="test-text">如果您看到这个页面，说明 Vue 渲染正常！</text>
      <view class="test-info">
        <text>Vue 版本: {{ vueVersion }}</text>
      </view>
      <view class="test-info">
        <text>页面路径: {{ currentPath }}</text>
      </view>
      <view class="test-info">
        <text>时间戳: {{ timestamp }}</text>
      </view>
    </view>
    <view class="test-buttons">
      <button class="test-button" @click="testClick">点击测试</button>
      <button class="test-button" @click="goToLogin">返回登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      vueVersion: 'Vue 3',
      currentPath: '',
      timestamp: '',
      clickCount: 0
    }
  },
  
  onLoad() {
    console.log('🎉 简单测试页面加载成功！');
    console.log('📱 当前时间:', new Date().toLocaleString());
    
    this.currentPath = this.$route?.path || 'unknown';
    this.timestamp = new Date().toLocaleString();
    
    // 测试 uView Plus
    if (this.$u) {
      console.log('📦 uView Plus 可用');
    } else {
      console.log('❌ uView Plus 不可用');
    }
  },
  
  onShow() {
    console.log('👀 简单测试页面显示');
  },
  
  methods: {
    testClick() {
      this.clickCount++;
      console.log(`🖱️ 按钮点击 ${this.clickCount} 次`);
      
      // 测试 uni API
      uni.showToast({
        title: `点击了 ${this.clickCount} 次`,
        icon: 'success'
      });
    },
    
    goToLogin() {
      console.log('🔄 跳转到登录页面');
      uni.navigateTo({
        url: '/pages/sys/login/index'
      });
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
}

.test-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.test-text {
  font-size: 32rpx;
  color: #333;
  text-align: center;
  display: block;
  margin-bottom: 40rpx;
}

.test-info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
}

.test-info text {
  font-size: 28rpx;
  color: #666;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-button {
  background-color: #007AFF;
  color: white;
  border: none;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
}

.test-button:active {
  background-color: #0056CC;
}
</style>
