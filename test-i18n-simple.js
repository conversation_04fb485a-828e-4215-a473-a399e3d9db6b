/**
 * 简单测试 i18n 配置是否正确
 */

async function testI18nConfig() {
    console.log('🔍 测试 i18n 配置...');
    
    try {
        // 测试语言包导入
        const zhCN = await import('./common/locales/zh_CN.js');
        const en = await import('./common/locales/en.js');
        
        console.log('✅ 中文语言包导入成功');
        console.log('✅ 英文语言包导入成功');
        
        // 检查语言包内容
        console.log('📋 中文语言包内容:');
        console.log('  login.placeholderAccount:', zhCN.default.login.placeholderAccount);
        console.log('  login.placeholderPassword:', zhCN.default.login.placeholderPassword);
        console.log('  login.forget:', zhCN.default.login.forget);
        console.log('  login.autoLogin:', zhCN.default.login.autoLogin);
        console.log('  login.reg:', zhCN.default.login.reg);
        
        console.log('📋 英文语言包内容:');
        console.log('  login.placeholderAccount:', en.default.login.placeholderAccount);
        console.log('  login.placeholderPassword:', en.default.login.placeholderPassword);
        console.log('  login.forget:', en.default.login.forget);
        console.log('  login.autoLogin:', en.default.login.autoLogin);
        console.log('  login.reg:', en.default.login.reg);
        
        // 测试 vue-i18n 导入
        const { createI18n } = await import('vue-i18n');
        console.log('✅ vue-i18n 导入成功');
        
        // 创建 i18n 实例测试
        const i18n = createI18n({
            legacy: true,
            locale: 'zh_CN',
            fallbackLocale: 'zh_CN',
            messages: {
                'zh_CN': zhCN.default,
                'en': en.default,
            },
            globalInjection: true
        });
        
        console.log('✅ i18n 实例创建成功');
        console.log('📋 i18n 配置:', {
            locale: i18n.global.locale,
            fallbackLocale: i18n.global.fallbackLocale,
            legacy: i18n.mode === 'legacy',
            globalInjection: i18n.global.globalInjection
        });
        
        // 测试翻译功能
        const t = i18n.global.t;
        console.log('🧪 测试翻译功能:');
        console.log('  t("login.placeholderAccount"):', t('login.placeholderAccount'));
        console.log('  t("login.placeholderPassword"):', t('login.placeholderPassword'));
        console.log('  t("login.forget"):', t('login.forget'));
        console.log('  t("login.autoLogin"):', t('login.autoLogin'));
        console.log('  t("login.reg"):', t('login.reg'));
        
        console.log('🎉 i18n 配置测试完成！');
        
    } catch (error) {
        console.error('❌ i18n 配置测试失败:', error);
    }
}

testI18nConfig();
