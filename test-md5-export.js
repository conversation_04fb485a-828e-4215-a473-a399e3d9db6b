/**
 * 测试 md5.js 的导出是否正确
 */

async function testMd5Export() {
  console.log('🔍 测试 md5.js 导出...\n');
  
  try {
    // 尝试导入 md5 模块
    console.log('📦 尝试导入 md5 模块...');
    const md5Module = await import('./common/md5.js');
    
    console.log('✅ 导入成功');
    console.log('📋 模块内容:', Object.keys(md5Module));
    
    // 检查默认导出
    if (md5Module.default) {
      console.log('✅ 默认导出存在');
      console.log('📋 默认导出类型:', typeof md5Module.default);
      
      // 检查是否是函数
      if (typeof md5Module.default === 'function') {
        console.log('✅ md5 是一个函数');
        
        // 测试 md5 功能
        const testString = 'Hello World';
        const hash = md5Module.default(testString);
        console.log(`🧪 测试 MD5: "${testString}" -> "${hash}"`);
        
        // 验证结果是否正确 (Hello World 的 MD5 应该是 b10a8db164e0754105b7a99be72e3fe5)
        const expectedHash = 'b10a8db164e0754105b7a99be72e3fe5';
        if (hash === expectedHash) {
          console.log('✅ MD5 计算结果正确');
        } else {
          console.log(`⚠️ MD5 计算结果不匹配，期望: ${expectedHash}, 实际: ${hash}`);
        }
        
        // 测试其他方法
        if (md5Module.default.create && typeof md5Module.default.create === 'function') {
          console.log('✅ create 方法可用');
        }
        
        if (md5Module.default.update && typeof md5Module.default.update === 'function') {
          console.log('✅ update 方法可用');
        }
        
      } else {
        console.log('❌ md5 不是一个函数，类型:', typeof md5Module.default);
      }
    } else {
      console.log('❌ 默认导出不存在');
    }
    
    console.log('\n🎉 md5.js 导出测试完成');
    return true;
    
  } catch (error) {
    console.error('❌ 导入失败:', error.message);
    console.error('错误详情:', error);
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查 md5.js 的导出语法');
    console.log('2. 确保文件路径正确');
    console.log('3. 验证 ES6 模块语法');
    
    return false;
  }
}

// 运行测试
testMd5Export().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
