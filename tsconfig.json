{"compilerOptions": {"sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"], "target": "esnext", "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "noEmit": true}, "include": ["**/*.vue", "**/*.ts", "**/*.js"], "exclude": ["node_modules", "unpackage", "dist"]}