/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
@import 'uview-plus/theme.scss';
$u-main-color: #303133;
$u-content-color: #505256;
$u-tips-color: #909399;
$u-light-color: #c0c4cc;
$u-border-color: #dedfe2;
$u-bg-color: #f3f4f6;

$u-type-primary: #2979ff;
$u-type-primary-light: #ecf5ff;
$u-type-primary-disabled: #a0cfff;
$u-type-primary-dark: #2b85e4;

$u-type-warning: #ff9900;
$u-type-warning-disabled: #fcbd71;
$u-type-warning-dark: #f29100;
$u-type-warning-light: #fdf6ec;

$u-type-success: #19be6b;
$u-type-success-disabled: #71d5a1;
$u-type-success-dark: #18b566;
$u-type-success-light: #dbf1e1;

$u-type-error: #fa3534;
$u-type-error-disabled: #fab6b6;
$u-type-error-dark: #dd6161;
$u-type-error-light: #fef0f0;

$u-type-info: #909399;
$u-type-info-disabled: #c8c9cc;
$u-type-info-dark: #82848a;
$u-type-info-light: #f4f4f5;

$u-form-item-height: 70rpx;
$u-form-item-border-color: #dcdfe6;
