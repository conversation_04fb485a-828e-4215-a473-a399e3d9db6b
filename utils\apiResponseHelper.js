/**
 * API 响应处理工具
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */

/**
 * 统一处理 API 响应数据
 * @param {*} response - API 响应数据
 * @param {string} dataType - 期望的数据类型 ('list', 'object', 'auto')
 * @returns {*} 处理后的数据
 */
export function processApiResponse(response, dataType = 'auto') {
  console.log('处理 API 响应:', response);
  
  // 如果响应为空或 null，返回默认值
  if (!response) {
    return dataType === 'list' ? [] : {};
  }
  
  // 如果响应是字符串，尝试解析为 JSON
  if (typeof response === 'string') {
    try {
      response = JSON.parse(response);
    } catch (e) {
      console.error('JSON 解析失败:', e);
      return dataType === 'list' ? [] : {};
    }
  }
  
  // 根据数据类型处理响应
  switch (dataType) {
    case 'list':
      // 期望列表数据
      if (Array.isArray(response)) {
        return response;
      } else if (response && typeof response === 'object') {
        // 检查常见的列表属性
        return response.list || response.data || response.items || [];
      }
      return [];
      
    case 'object':
      // 期望对象数据
      if (response && typeof response === 'object' && !Array.isArray(response)) {
        return response;
      }
      return {};
      
    case 'auto':
    default:
      // 自动判断数据类型
      if (Array.isArray(response)) {
        return response;
      } else if (response && typeof response === 'object') {
        // 如果有 list 属性，优先返回 list
        if (response.list !== undefined) {
          return response.list;
        }
        // 如果有 data 属性且是数组，返回 data
        if (Array.isArray(response.data)) {
          return response.data;
        }
        // 否则返回整个对象
        return response;
      }
      return response;
  }
}

/**
 * 处理分页响应数据
 * @param {*} response - API 响应数据
 * @returns {object} 包含 list, total, hasMore 等信息的对象
 */
export function processPaginationResponse(response) {
  const processed = processApiResponse(response, 'auto');
  
  if (Array.isArray(processed)) {
    return {
      list: processed,
      total: processed.length,
      hasMore: false
    };
  } else if (processed && typeof processed === 'object') {
    return {
      list: processed.list || processed.data || [],
      total: processed.total || processed.count || 0,
      hasMore: processed.hasMore || false,
      page: processed.page || 1,
      size: processed.size || 10
    };
  }
  
  return {
    list: [],
    total: 0,
    hasMore: false
  };
}

/**
 * 安全的 JSON 解析
 * @param {string} jsonString - JSON 字符串
 * @param {*} defaultValue - 解析失败时的默认值
 * @returns {*} 解析后的对象或默认值
 */
export function safeJsonParse(jsonString, defaultValue = null) {
  if (typeof jsonString !== 'string') {
    return jsonString;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error('JSON 解析失败:', e, '原始数据:', jsonString);
    return defaultValue;
  }
}

/**
 * 检查响应是否为成功状态
 * @param {*} response - API 响应数据
 * @returns {boolean} 是否成功
 */
export function isApiSuccess(response) {
  if (!response) return false;
  
  // 检查常见的成功状态字段
  if (response.code !== undefined) {
    return response.code === 0 || response.code === '0' || response.code === 200;
  }
  
  if (response.status !== undefined) {
    return response.status === 0 || response.status === '0' || response.status === 200;
  }
  
  if (response.success !== undefined) {
    return response.success === true || response.success === 'true';
  }
  
  // 如果没有明确的状态字段，假设成功
  return true;
}

/**
 * 获取 API 错误消息
 * @param {*} response - API 响应数据
 * @returns {string} 错误消息
 */
export function getApiErrorMessage(response) {
  if (!response) return '未知错误';
  
  // 检查常见的错误消息字段
  return response.msg || 
         response.message || 
         response.error || 
         response.errorMessage || 
         '请求失败';
}

export default {
  processApiResponse,
  processPaginationResponse,
  safeJsonParse,
  isApiSuccess,
  getApiErrorMessage
};
