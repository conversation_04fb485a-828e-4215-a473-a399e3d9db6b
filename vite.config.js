import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni()
    // visualizer() - 已移除，如需要请安装: pnpm add -D rollup-plugin-visualizer
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // 取消sass废弃API的报警
        silenceDeprecations: ['legacy-js-api', 'color-functions', 'import'],
      },
    },
  },
  server: {
    port: 5100,
    fs: {
      // Allow serving files from one level up to the project root
      allow: ['..']
    },
    // 代理配置（如果需要的话）
    proxy: {
      '/pda': {
        target: 'http://127.0.0.1:8980',
        // target: 'https://hcscmtest.zzfzyc.com/hcscm/pda/v1',
        changeOrigin: true,
        secure: false
      }
    }
  },
  // 处理 uview-plus 的 ES 模块兼容性
  optimizeDeps: {
    include: ['uview-plus']
  },
  build: {
    sourcemap: true,
    // 确保 uview-plus 被正确处理
    commonjsOptions: {
      include: [/uview-plus/, /node_modules/]
    }
  },
  // 解决 import.meta.glob 兼容性问题
  define: {
    // 为旧版本浏览器提供 import.meta 兼容性
    'import.meta.glob': 'undefined'
  }
});
